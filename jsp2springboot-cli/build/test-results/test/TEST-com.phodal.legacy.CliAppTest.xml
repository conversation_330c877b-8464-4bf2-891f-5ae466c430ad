<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.phodal.legacy.CliAppTest" tests="11" skipped="0" failures="0" errors="0" timestamp="2025-07-09T14:18:32.517Z" hostname="fdhuang.local" time="0.391">
  <properties/>
  <testcase name="testAnalyzeCommandWithOptions()" classname="com.phodal.legacy.CliAppTest" time="0.262"/>
  <testcase name="testConvertCommandWithInvalidSource()" classname="com.phodal.legacy.CliAppTest" time="0.009"/>
  <testcase name="testValidateCommandWithValidDirectory()" classname="com.phodal.legacy.CliAppTest" time="0.009"/>
  <testcase name="testVersionCommand()" classname="com.phodal.legacy.CliAppTest" time="0.009"/>
  <testcase name="testConvertCommandWithCustomOptions()" classname="com.phodal.legacy.CliAppTest" time="0.008"/>
  <testcase name="testAnalyzeCommandWithValidDirectory()" classname="com.phodal.legacy.CliAppTest" time="0.017"/>
  <testcase name="testAnalyzeCommandWithInvalidDirectory()" classname="com.phodal.legacy.CliAppTest" time="0.008"/>
  <testcase name="testConvertCommandWithValidDirectories()" classname="com.phodal.legacy.CliAppTest" time="0.01"/>
  <testcase name="testHelpCommand()" classname="com.phodal.legacy.CliAppTest" time="0.034"/>
  <testcase name="testMainCommandWithoutArguments()" classname="com.phodal.legacy.CliAppTest" time="0.007"/>
  <testcase name="testValidateCommandWithInvalidDirectory()" classname="com.phodal.legacy.CliAppTest" time="0.009"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
