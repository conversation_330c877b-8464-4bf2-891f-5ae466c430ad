<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.phodal.legacy.services.AnalysisServiceTest" tests="6" skipped="0" failures="0" errors="0" timestamp="2025-07-09T14:18:33.300Z" hostname="fdhuang.local" time="0.064">
  <properties/>
  <testcase name="testAnalyzeNonExistentProject()" classname="com.phodal.legacy.services.AnalysisServiceTest" time="0.003"/>
  <testcase name="testAnalyzeComplexProject()" classname="com.phodal.legacy.services.AnalysisServiceTest" time="0.025"/>
  <testcase name="testAnalyzeEmptyProject()" classname="com.phodal.legacy.services.AnalysisServiceTest" time="0.003"/>
  <testcase name="testAnalyzeWithSelectiveOptions()" classname="com.phodal.legacy.services.AnalysisServiceTest" time="0.015"/>
  <testcase name="testAnalysisOptions()" classname="com.phodal.legacy.services.AnalysisServiceTest" time="0.001"/>
  <testcase name="testAnalyzeSimpleProject()" classname="com.phodal.legacy.services.AnalysisServiceTest" time="0.015"/>
  <system-out><![CDATA[22:18:33.302 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeProject in component: AnalysisService
22:18:33.307 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeProject in component: AnalysisService
22:18:33.307 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Starting comprehensive analysis of project: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479
22:18:33.307 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing JSP files...
22:18:33.307 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJspFiles in component: JspAnalyzer
22:18:33.308 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 2 JSP files to analyze
22:18:33.308 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/2 ({:.1f}%) - 50.0
22:18:33.308 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/page1.jsp
22:18:33.308 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/page1.jsp
22:18:33.308 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/page1.jsp
22:18:33.309 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/page1.jsp
22:18:33.309 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 2/2 ({:.1f}%) - 100.0
22:18:33.309 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/page2.jsp
22:18:33.309 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/page2.jsp
22:18:33.309 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/page2.jsp
22:18:33.309 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/page2.jsp
22:18:33.309 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
22:18:33.309 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 2 JSP components
22:18:33.309 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing Java source files...
22:18:33.309 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
22:18:33.311 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 3 Java files to analyze
22:18:33.311 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/3 ({:.1f}%) - 33.33333333333333
22:18:33.311 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/src/main/java/com/example/TestClass.java
22:18:33.311 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/src/main/java/com/example/TestClass.java
22:18:33.311 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/src/main/java/com/example/TestClass.java
22:18:33.313 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/src/main/java/com/example/TestClass.java
22:18:33.313 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 2/3 ({:.1f}%) - 66.66666666666666
22:18:33.313 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/src/main/java/com/example/TestServlet.java
22:18:33.313 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/src/main/java/com/example/TestServlet.java
22:18:33.313 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/src/main/java/com/example/TestServlet.java
22:18:33.314 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/src/main/java/com/example/TestServlet.java
22:18:33.315 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 3/3 ({:.1f}%) - 100.0
22:18:33.315 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/src/main/java/com/example/TestFilter.java
22:18:33.315 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/src/main/java/com/example/TestFilter.java
22:18:33.315 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/src/main/java/com/example/TestFilter.java
22:18:33.316 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/src/main/java/com/example/TestFilter.java
22:18:33.316 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
22:18:33.316 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 3 Java components
22:18:33.316 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing web.xml files...
22:18:33.316 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeWebXmlFiles in component: WebXmlParser
22:18:33.317 [Test worker] INFO com.phodal.legacy.parser.WebXmlParser -- Found 1 web.xml files to analyze
22:18:33.317 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/1 ({:.1f}%) - 100.0
22:18:33.318 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/WEB-INF/web.xml
22:18:33.320 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/WEB-INF/web.xml
22:18:33.320 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeWebXmlFiles in component: WebXmlParser
22:18:33.320 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 web.xml components
22:18:33.320 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building JSP to bytecode mappings...
22:18:33.321 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Skipping mapping creation - no JSP or bytecode components found
22:18:33.321 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building dependency graph...
22:18:33.321 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 6 components
22:18:33.321 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
22:18:33.323 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeProject in component: AnalysisService
22:18:33.323 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analysis completed successfully in 16 ms
22:18:33.329 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeProject in component: AnalysisService
22:18:33.329 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Starting comprehensive analysis of project: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3066517253897908
22:18:33.329 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing JSP files...
22:18:33.329 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJspFiles in component: JspAnalyzer
22:18:33.330 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 0 JSP files to analyze
22:18:33.330 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
22:18:33.330 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 0 JSP components
22:18:33.330 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing Java source files...
22:18:33.330 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
22:18:33.330 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 0 Java files to analyze
22:18:33.330 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
22:18:33.330 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 0 Java components
22:18:33.330 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing web.xml files...
22:18:33.330 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeWebXmlFiles in component: WebXmlParser
22:18:33.330 [Test worker] INFO com.phodal.legacy.parser.WebXmlParser -- Found 0 web.xml files to analyze
22:18:33.330 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeWebXmlFiles in component: WebXmlParser
22:18:33.330 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 0 web.xml components
22:18:33.330 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building JSP to bytecode mappings...
22:18:33.330 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Skipping mapping creation - no JSP or bytecode components found
22:18:33.330 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building dependency graph...
22:18:33.330 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 0 components
22:18:33.330 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
22:18:33.331 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeProject in component: AnalysisService
22:18:33.331 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analysis completed successfully in 2 ms
22:18:33.334 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeProject in component: AnalysisService
22:18:33.334 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Starting comprehensive analysis of project: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4682541847964043572
22:18:33.335 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing JSP files...
22:18:33.335 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJspFiles in component: JspAnalyzer
22:18:33.336 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 1 JSP files to analyze
22:18:33.336 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/1 ({:.1f}%) - 100.0
22:18:33.336 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4682541847964043572/index.jsp
22:18:33.336 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4682541847964043572/index.jsp
22:18:33.336 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4682541847964043572/index.jsp
22:18:33.336 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4682541847964043572/index.jsp
22:18:33.336 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
22:18:33.336 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 JSP components
22:18:33.336 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building JSP to bytecode mappings...
22:18:33.336 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Skipping mapping creation - no JSP or bytecode components found
22:18:33.336 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building dependency graph...
22:18:33.336 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 1 components
22:18:33.337 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
22:18:33.338 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeProject in component: AnalysisService
22:18:33.338 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analysis completed successfully in 3 ms
22:18:33.338 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeProject in component: AnalysisService
22:18:33.338 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Starting comprehensive analysis of project: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4682541847964043572
22:18:33.338 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing Java source files...
22:18:33.338 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
22:18:33.339 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 1 Java files to analyze
22:18:33.339 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/1 ({:.1f}%) - 100.0
22:18:33.339 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4682541847964043572/src/main/java/com/example/TestServlet.java
22:18:33.339 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4682541847964043572/src/main/java/com/example/TestServlet.java
22:18:33.339 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4682541847964043572/src/main/java/com/example/TestServlet.java
22:18:33.341 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4682541847964043572/src/main/java/com/example/TestServlet.java
22:18:33.342 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
22:18:33.342 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 Java components
22:18:33.342 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building JSP to bytecode mappings...
22:18:33.342 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Skipping mapping creation - no JSP or bytecode components found
22:18:33.342 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building dependency graph...
22:18:33.342 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 1 components
22:18:33.342 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
22:18:33.343 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeProject in component: AnalysisService
22:18:33.343 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analysis completed successfully in 5 ms
22:18:33.351 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeProject in component: AnalysisService
22:18:33.351 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Starting comprehensive analysis of project: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10576135265167668831
22:18:33.351 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing JSP files...
22:18:33.351 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJspFiles in component: JspAnalyzer
22:18:33.352 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 1 JSP files to analyze
22:18:33.352 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/1 ({:.1f}%) - 100.0
22:18:33.352 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10576135265167668831/index.jsp
22:18:33.352 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10576135265167668831/index.jsp
22:18:33.353 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10576135265167668831/index.jsp
22:18:33.353 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10576135265167668831/index.jsp
22:18:33.353 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
22:18:33.353 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 JSP components
22:18:33.353 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing Java source files...
22:18:33.353 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
22:18:33.354 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 1 Java files to analyze
22:18:33.354 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/1 ({:.1f}%) - 100.0
22:18:33.354 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10576135265167668831/src/main/java/com/example/TestServlet.java
22:18:33.354 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10576135265167668831/src/main/java/com/example/TestServlet.java
22:18:33.354 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10576135265167668831/src/main/java/com/example/TestServlet.java
22:18:33.356 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10576135265167668831/src/main/java/com/example/TestServlet.java
22:18:33.356 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
22:18:33.356 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 Java components
22:18:33.356 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing web.xml files...
22:18:33.356 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeWebXmlFiles in component: WebXmlParser
22:18:33.357 [Test worker] INFO com.phodal.legacy.parser.WebXmlParser -- Found 1 web.xml files to analyze
22:18:33.357 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/1 ({:.1f}%) - 100.0
22:18:33.357 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10576135265167668831/WEB-INF/web.xml
22:18:33.359 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10576135265167668831/WEB-INF/web.xml
22:18:33.359 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeWebXmlFiles in component: WebXmlParser
22:18:33.359 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 web.xml components
22:18:33.359 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building JSP to bytecode mappings...
22:18:33.359 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Skipping mapping creation - no JSP or bytecode components found
22:18:33.359 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building dependency graph...
22:18:33.359 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 3 components
22:18:33.360 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
22:18:33.361 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeProject in component: AnalysisService
22:18:33.361 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analysis completed successfully in 10 ms
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
