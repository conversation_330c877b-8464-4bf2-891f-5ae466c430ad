<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.phodal.legacy.integration.JspBytecodeMappingIntegrationTest" tests="4" skipped="0" failures="0" errors="0" timestamp="2025-07-09T14:18:32.912Z" hostname="fdhuang.local" time="0.036">
  <properties/>
  <testcase name="testMappingWithComplexDependencies()" classname="com.phodal.legacy.integration.JspBytecodeMappingIntegrationTest" time="0.011"/>
  <testcase name="testJspAnalysisWithMappingHints()" classname="com.phodal.legacy.integration.JspBytecodeMappingIntegrationTest" time="0.017"/>
  <testcase name="testEndToEndMappingFlow()" classname="com.phodal.legacy.integration.JspBytecodeMappingIntegrationTest" time="0.003"/>
  <testcase name="testMappingWithTagLibraries()" classname="com.phodal.legacy.integration.JspBytecodeMappingIntegrationTest" time="0.002"/>
  <system-out><![CDATA[22:18:32.914 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: createMappings in component: JspBytecodeMapper
22:18:32.917 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Creating mappings for 1 JSP components and 1 bytecode components
22:18:32.921 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Successfully created 1 mappings out of 1 JSP components
22:18:32.921 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: createMappings in component: JspBytecodeMapper
22:18:32.927 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1900289694880460334/test.jsp
22:18:32.927 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1900289694880460334/test.jsp
22:18:32.928 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1900289694880460334/test.jsp
22:18:32.933 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1900289694880460334/test.jsp
22:18:32.933 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1900289694880460334/test.jsp
22:18:32.933 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1900289694880460334/test.jsp
22:18:32.943 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: createMappings in component: JspBytecodeMapper
22:18:32.943 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Creating mappings for 2 JSP components and 3 bytecode components
22:18:32.943 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Successfully created 2 mappings out of 2 JSP components
22:18:32.943 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: createMappings in component: JspBytecodeMapper
22:18:32.947 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: createMappings in component: JspBytecodeMapper
22:18:32.947 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Creating mappings for 1 JSP components and 1 bytecode components
22:18:32.947 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Successfully created 1 mappings out of 1 JSP components
22:18:32.947 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: createMappings in component: JspBytecodeMapper
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
