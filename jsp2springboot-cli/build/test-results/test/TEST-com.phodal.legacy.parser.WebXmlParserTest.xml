<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.phodal.legacy.parser.WebXmlParserTest" tests="9" skipped="0" failures="0" errors="0" timestamp="2025-07-09T14:18:33.200Z" hostname="fdhuang.local" time="0.098">
  <properties/>
  <testcase name="testAnalyzeWebXmlWithErrorPages()" classname="com.phodal.legacy.parser.WebXmlParserTest" time="0.052"/>
  <testcase name="testAnalyzeSimpleWebXml()" classname="com.phodal.legacy.parser.WebXmlParserTest" time="0.004"/>
  <testcase name="testAnalyzeMultipleWebXmlFiles()" classname="com.phodal.legacy.parser.WebXmlParserTest" time="0.009"/>
  <testcase name="testAnalyzeWebXmlWithServlets()" classname="com.phodal.legacy.parser.WebXmlParserTest" time="0.008"/>
  <testcase name="testAnalyzeEmptyDirectory()" classname="com.phodal.legacy.parser.WebXmlParserTest" time="0.002"/>
  <testcase name="testAnalyzeWebXmlWithFilters()" classname="com.phodal.legacy.parser.WebXmlParserTest" time="0.005"/>
  <testcase name="testAnalyzeWebXmlWithListeners()" classname="com.phodal.legacy.parser.WebXmlParserTest" time="0.004"/>
  <testcase name="testAnalyzeWebXmlWithWelcomeFiles()" classname="com.phodal.legacy.parser.WebXmlParserTest" time="0.003"/>
  <testcase name="testAnalyzeWebXmlWithContextParams()" classname="com.phodal.legacy.parser.WebXmlParserTest" time="0.004"/>
  <system-out><![CDATA[22:18:33.201 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit6453507154098270630/web.xml
22:18:33.250 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit6453507154098270630/web.xml
22:18:33.254 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2372567295515063565/web.xml
22:18:33.256 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2372567295515063565/web.xml
22:18:33.260 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeWebXmlFiles in component: WebXmlParser
22:18:33.261 [Test worker] INFO com.phodal.legacy.parser.WebXmlParser -- Found 2 web.xml files to analyze
22:18:33.261 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/2 ({:.1f}%) - 50.0
22:18:33.261 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15669538246054765958/sub/WEB-INF/web.xml
22:18:33.263 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15669538246054765958/sub/WEB-INF/web.xml
22:18:33.263 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 2/2 ({:.1f}%) - 100.0
22:18:33.263 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15669538246054765958/WEB-INF/web.xml
22:18:33.265 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15669538246054765958/WEB-INF/web.xml
22:18:33.265 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeWebXmlFiles in component: WebXmlParser
22:18:33.269 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7531024711261627118/web.xml
22:18:33.274 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7531024711261627118/web.xml
22:18:33.278 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeWebXmlFiles in component: WebXmlParser
22:18:33.279 [Test worker] INFO com.phodal.legacy.parser.WebXmlParser -- Found 0 web.xml files to analyze
22:18:33.279 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeWebXmlFiles in component: WebXmlParser
22:18:33.281 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit5277120465433637905/web.xml
22:18:33.284 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit5277120465433637905/web.xml
22:18:33.287 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11655056075956336327/web.xml
22:18:33.288 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11655056075956336327/web.xml
22:18:33.291 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit12971958635389970998/web.xml
22:18:33.292 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit12971958635389970998/web.xml
22:18:33.295 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1163294882632423325/web.xml
22:18:33.297 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1163294882632423325/web.xml
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
