<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.phodal.legacy.model.DependencyGraphTest" tests="12" skipped="0" failures="0" errors="0" timestamp="2025-07-09T14:18:32.962Z" hostname="fdhuang.local" time="0.018">
  <properties/>
  <testcase name="testEmptyGraph()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.001"/>
  <testcase name="testGetNodesByType()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.0"/>
  <testcase name="testNodeMigrationPriority()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.001"/>
  <testcase name="testCalculateDepths()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.001"/>
  <testcase name="testGetRootAndLeafNodes()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.001"/>
  <testcase name="testBuildSimpleDependencies()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.001"/>
  <testcase name="testAddMultipleComponents()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.001"/>
  <testcase name="testCircularDependencyDetection()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.001"/>
  <testcase name="testMigrationOrder()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.001"/>
  <testcase name="testGraphStatistics()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.001"/>
  <testcase name="testAddSingleComponent()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.0"/>
  <testcase name="testTopologicalSort()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.001"/>
  <system-out><![CDATA[22:18:32.963 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 0 components
22:18:32.963 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
22:18:32.965 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 3 components
22:18:32.966 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
22:18:32.967 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 4 components
22:18:32.967 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
22:18:32.969 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 5 components
22:18:32.969 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
22:18:32.970 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 2 components
22:18:32.971 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
22:18:32.973 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 3 components
22:18:32.973 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
22:18:32.974 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 3 components
22:18:32.975 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
22:18:32.977 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 4 components
22:18:32.977 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
22:18:32.979 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 3 components
22:18:32.980 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
