<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.phodal.legacy.utils.FileUtilsTest" tests="15" skipped="0" failures="0" errors="0" timestamp="2025-07-09T14:18:33.365Z" hostname="fdhuang.local" time="0.057">
  <properties/>
  <testcase name="testIsJavaFile()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.002"/>
  <testcase name="testFindWebXmlFiles()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.004"/>
  <testcase name="testBackupFile()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.006"/>
  <testcase name="testIsJspFile()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.001"/>
  <testcase name="testEnsureDirectoryExists()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.003"/>
  <testcase name="testWriteFileContentCreatesDirectories()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.004"/>
  <testcase name="testFindJspFiles()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.005"/>
  <testcase name="testCountFilesByExtension()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.004"/>
  <testcase name="testReadAndWriteFileContent()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.003"/>
  <testcase name="testCopyFile()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.004"/>
  <testcase name="testGetFileNameWithoutExtension()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.002"/>
  <testcase name="testGetFileExtension()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.002"/>
  <testcase name="testFindJavaFiles()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.004"/>
  <testcase name="testGetRelativePath()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.002"/>
  <testcase name="testFindFilesByExtensions()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.004"/>
  <system-out><![CDATA[22:18:33.370 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: findWebXmlFiles in component: FileUtils
22:18:33.370 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Found 1 web.xml files in /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4368477349030831862/testdir
22:18:33.370 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: findWebXmlFiles in component: FileUtils
22:18:33.373 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Writing file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10566404771582481084/test.txt
22:18:33.374 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Written file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10566404771582481084/test.txt
22:18:33.376 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Created backup: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10566404771582481084/test.txt.backup.1752070713374
22:18:33.376 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10566404771582481084/test.txt.backup.1752070713374
22:18:33.376 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10566404771582481084/test.txt.backup.1752070713374
22:18:33.386 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Writing file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15336158664936015399/nested/deep/file.txt
22:18:33.387 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Written file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15336158664936015399/nested/deep/file.txt
22:18:33.387 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15336158664936015399/nested/deep/file.txt
22:18:33.387 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15336158664936015399/nested/deep/file.txt
22:18:33.391 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: findFilesByExtensions in component: FileUtils
22:18:33.392 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Found 3 files with extensions [.jsp, .tag, .tagx, .jspx] in /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit17478456195328336471/testdir
22:18:33.392 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: findFilesByExtensions in component: FileUtils
22:18:33.401 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Writing file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7298038955033786787/test.txt
22:18:33.401 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Written file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7298038955033786787/test.txt
22:18:33.401 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7298038955033786787/test.txt
22:18:33.402 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7298038955033786787/test.txt
22:18:33.404 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Writing file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7776739327439513371/test.txt
22:18:33.405 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Written file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7776739327439513371/test.txt
22:18:33.405 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Copying to /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7776739327439513371/copied.txt file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7776739327439513371/test.txt
22:18:33.405 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Copied file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7776739327439513371/copied.txt
22:18:33.405 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7776739327439513371/copied.txt
22:18:33.406 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7776739327439513371/copied.txt
22:18:33.413 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: findFilesByExtensions in component: FileUtils
22:18:33.413 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Found 2 files with extensions [.java] in /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit14305589071148643290/testdir
22:18:33.413 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: findFilesByExtensions in component: FileUtils
22:18:33.420 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: findFilesByExtensions in component: FileUtils
22:18:33.420 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Found 2 files with extensions [.jsp] in /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit380517712491201667/testdir
22:18:33.420 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: findFilesByExtensions in component: FileUtils
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
