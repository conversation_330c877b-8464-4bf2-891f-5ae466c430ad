<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.phodal.legacy.parser.JspAnalyzerTest" tests="1" skipped="0" failures="0" errors="0" timestamp="2025-07-09T14:01:20.737Z" hostname="fdhuang.local" time="0.098">
  <properties/>
  <testcase name="testAnalyzeJspWithImports()" classname="com.phodal.legacy.parser.JspAnalyzerTest" time="0.098"/>
  <system-out><![CDATA[22:01:20.812 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11225011581160520704/complex.jsp
22:01:20.817 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11225011581160520704/complex.jsp
22:01:20.819 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11225011581160520704/complex.jsp
22:01:20.823 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11225011581160520704/complex.jsp
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
