<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - AnalysisServiceTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>AnalysisServiceTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.phodal.legacy.services.html">com.phodal.legacy.services</a> &gt; AnalysisServiceTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">6</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.062s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
<li>
<a href="#">Standard output</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">testAnalysisOptions()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeComplexProject()</td>
<td class="success">0.025s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeEmptyProject()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeNonExistentProject()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeSimpleProject()</td>
<td class="success">0.015s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeWithSelectiveOptions()</td>
<td class="success">0.015s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>22:18:33.302 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeProject in component: AnalysisService
22:18:33.307 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeProject in component: AnalysisService
22:18:33.307 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Starting comprehensive analysis of project: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479
22:18:33.307 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing JSP files...
22:18:33.307 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeJspFiles in component: JspAnalyzer
22:18:33.308 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 2 JSP files to analyze
22:18:33.308 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 1/2 ({:.1f}%) - 50.0
22:18:33.308 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/page1.jsp
22:18:33.308 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/page1.jsp
22:18:33.308 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/page1.jsp
22:18:33.309 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/page1.jsp
22:18:33.309 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 2/2 ({:.1f}%) - 100.0
22:18:33.309 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/page2.jsp
22:18:33.309 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/page2.jsp
22:18:33.309 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/page2.jsp
22:18:33.309 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/page2.jsp
22:18:33.309 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
22:18:33.309 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 2 JSP components
22:18:33.309 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing Java source files...
22:18:33.309 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
22:18:33.311 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 3 Java files to analyze
22:18:33.311 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 1/3 ({:.1f}%) - 33.33333333333333
22:18:33.311 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/src/main/java/com/example/TestClass.java
22:18:33.311 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/src/main/java/com/example/TestClass.java
22:18:33.311 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/src/main/java/com/example/TestClass.java
22:18:33.313 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/src/main/java/com/example/TestClass.java
22:18:33.313 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 2/3 ({:.1f}%) - 66.66666666666666
22:18:33.313 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/src/main/java/com/example/TestServlet.java
22:18:33.313 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/src/main/java/com/example/TestServlet.java
22:18:33.313 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/src/main/java/com/example/TestServlet.java
22:18:33.314 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/src/main/java/com/example/TestServlet.java
22:18:33.315 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 3/3 ({:.1f}%) - 100.0
22:18:33.315 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/src/main/java/com/example/TestFilter.java
22:18:33.315 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/src/main/java/com/example/TestFilter.java
22:18:33.315 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/src/main/java/com/example/TestFilter.java
22:18:33.316 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/src/main/java/com/example/TestFilter.java
22:18:33.316 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
22:18:33.316 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 3 Java components
22:18:33.316 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing web.xml files...
22:18:33.316 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeWebXmlFiles in component: WebXmlParser
22:18:33.317 [Test worker] INFO com.phodal.legacy.parser.WebXmlParser -- Found 1 web.xml files to analyze
22:18:33.317 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 1/1 ({:.1f}%) - 100.0
22:18:33.318 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/WEB-INF/web.xml
22:18:33.320 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4637889336262348479/WEB-INF/web.xml
22:18:33.320 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeWebXmlFiles in component: WebXmlParser
22:18:33.320 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 web.xml components
22:18:33.320 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building JSP to bytecode mappings...
22:18:33.321 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Skipping mapping creation - no JSP or bytecode components found
22:18:33.321 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building dependency graph...
22:18:33.321 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 6 components
22:18:33.321 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
22:18:33.323 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeProject in component: AnalysisService
22:18:33.323 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analysis completed successfully in 16 ms
22:18:33.329 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeProject in component: AnalysisService
22:18:33.329 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Starting comprehensive analysis of project: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3066517253897908
22:18:33.329 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing JSP files...
22:18:33.329 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeJspFiles in component: JspAnalyzer
22:18:33.330 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 0 JSP files to analyze
22:18:33.330 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
22:18:33.330 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 0 JSP components
22:18:33.330 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing Java source files...
22:18:33.330 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
22:18:33.330 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 0 Java files to analyze
22:18:33.330 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
22:18:33.330 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 0 Java components
22:18:33.330 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing web.xml files...
22:18:33.330 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeWebXmlFiles in component: WebXmlParser
22:18:33.330 [Test worker] INFO com.phodal.legacy.parser.WebXmlParser -- Found 0 web.xml files to analyze
22:18:33.330 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeWebXmlFiles in component: WebXmlParser
22:18:33.330 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 0 web.xml components
22:18:33.330 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building JSP to bytecode mappings...
22:18:33.330 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Skipping mapping creation - no JSP or bytecode components found
22:18:33.330 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building dependency graph...
22:18:33.330 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 0 components
22:18:33.330 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
22:18:33.331 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeProject in component: AnalysisService
22:18:33.331 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analysis completed successfully in 2 ms
22:18:33.334 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeProject in component: AnalysisService
22:18:33.334 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Starting comprehensive analysis of project: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4682541847964043572
22:18:33.335 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing JSP files...
22:18:33.335 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeJspFiles in component: JspAnalyzer
22:18:33.336 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 1 JSP files to analyze
22:18:33.336 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 1/1 ({:.1f}%) - 100.0
22:18:33.336 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4682541847964043572/index.jsp
22:18:33.336 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4682541847964043572/index.jsp
22:18:33.336 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4682541847964043572/index.jsp
22:18:33.336 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4682541847964043572/index.jsp
22:18:33.336 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
22:18:33.336 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 JSP components
22:18:33.336 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building JSP to bytecode mappings...
22:18:33.336 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Skipping mapping creation - no JSP or bytecode components found
22:18:33.336 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building dependency graph...
22:18:33.336 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 1 components
22:18:33.337 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
22:18:33.338 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeProject in component: AnalysisService
22:18:33.338 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analysis completed successfully in 3 ms
22:18:33.338 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeProject in component: AnalysisService
22:18:33.338 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Starting comprehensive analysis of project: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4682541847964043572
22:18:33.338 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing Java source files...
22:18:33.338 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
22:18:33.339 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 1 Java files to analyze
22:18:33.339 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 1/1 ({:.1f}%) - 100.0
22:18:33.339 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4682541847964043572/src/main/java/com/example/TestServlet.java
22:18:33.339 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4682541847964043572/src/main/java/com/example/TestServlet.java
22:18:33.339 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4682541847964043572/src/main/java/com/example/TestServlet.java
22:18:33.341 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit4682541847964043572/src/main/java/com/example/TestServlet.java
22:18:33.342 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
22:18:33.342 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 Java components
22:18:33.342 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building JSP to bytecode mappings...
22:18:33.342 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Skipping mapping creation - no JSP or bytecode components found
22:18:33.342 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building dependency graph...
22:18:33.342 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 1 components
22:18:33.342 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
22:18:33.343 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeProject in component: AnalysisService
22:18:33.343 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analysis completed successfully in 5 ms
22:18:33.351 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeProject in component: AnalysisService
22:18:33.351 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Starting comprehensive analysis of project: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10576135265167668831
22:18:33.351 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing JSP files...
22:18:33.351 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeJspFiles in component: JspAnalyzer
22:18:33.352 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 1 JSP files to analyze
22:18:33.352 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 1/1 ({:.1f}%) - 100.0
22:18:33.352 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10576135265167668831/index.jsp
22:18:33.352 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10576135265167668831/index.jsp
22:18:33.353 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10576135265167668831/index.jsp
22:18:33.353 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10576135265167668831/index.jsp
22:18:33.353 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
22:18:33.353 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 JSP components
22:18:33.353 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing Java source files...
22:18:33.353 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
22:18:33.354 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 1 Java files to analyze
22:18:33.354 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 1/1 ({:.1f}%) - 100.0
22:18:33.354 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10576135265167668831/src/main/java/com/example/TestServlet.java
22:18:33.354 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10576135265167668831/src/main/java/com/example/TestServlet.java
22:18:33.354 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10576135265167668831/src/main/java/com/example/TestServlet.java
22:18:33.356 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10576135265167668831/src/main/java/com/example/TestServlet.java
22:18:33.356 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
22:18:33.356 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 Java components
22:18:33.356 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing web.xml files...
22:18:33.356 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeWebXmlFiles in component: WebXmlParser
22:18:33.357 [Test worker] INFO com.phodal.legacy.parser.WebXmlParser -- Found 1 web.xml files to analyze
22:18:33.357 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 1/1 ({:.1f}%) - 100.0
22:18:33.357 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10576135265167668831/WEB-INF/web.xml
22:18:33.359 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10576135265167668831/WEB-INF/web.xml
22:18:33.359 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeWebXmlFiles in component: WebXmlParser
22:18:33.359 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 web.xml components
22:18:33.359 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building JSP to bytecode mappings...
22:18:33.359 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Skipping mapping creation - no JSP or bytecode components found
22:18:33.359 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building dependency graph...
22:18:33.359 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 3 components
22:18:33.360 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
22:18:33.361 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeProject in component: AnalysisService
22:18:33.361 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analysis completed successfully in 10 ms
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at Jul 9, 2025, 10:18:33 PM</p>
</div>
</div>
</body>
</html>
