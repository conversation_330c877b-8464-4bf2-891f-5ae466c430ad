<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - WebXmlParserTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>WebXmlParserTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.phodal.legacy.parser.html">com.phodal.legacy.parser</a> &gt; WebXmlParserTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">9</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.091s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
<li>
<a href="#">Standard output</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">testAnalyzeEmptyDirectory()</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeMultipleWebXmlFiles()</td>
<td class="success">0.009s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeSimpleWebXml()</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeWebXmlWithContextParams()</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeWebXmlWithErrorPages()</td>
<td class="success">0.052s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeWebXmlWithFilters()</td>
<td class="success">0.005s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeWebXmlWithListeners()</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeWebXmlWithServlets()</td>
<td class="success">0.008s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeWebXmlWithWelcomeFiles()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>22:18:33.201 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit6453507154098270630/web.xml
22:18:33.250 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit6453507154098270630/web.xml
22:18:33.254 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2372567295515063565/web.xml
22:18:33.256 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2372567295515063565/web.xml
22:18:33.260 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeWebXmlFiles in component: WebXmlParser
22:18:33.261 [Test worker] INFO com.phodal.legacy.parser.WebXmlParser -- Found 2 web.xml files to analyze
22:18:33.261 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 1/2 ({:.1f}%) - 50.0
22:18:33.261 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15669538246054765958/sub/WEB-INF/web.xml
22:18:33.263 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15669538246054765958/sub/WEB-INF/web.xml
22:18:33.263 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 2/2 ({:.1f}%) - 100.0
22:18:33.263 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15669538246054765958/WEB-INF/web.xml
22:18:33.265 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15669538246054765958/WEB-INF/web.xml
22:18:33.265 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeWebXmlFiles in component: WebXmlParser
22:18:33.269 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7531024711261627118/web.xml
22:18:33.274 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7531024711261627118/web.xml
22:18:33.278 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeWebXmlFiles in component: WebXmlParser
22:18:33.279 [Test worker] INFO com.phodal.legacy.parser.WebXmlParser -- Found 0 web.xml files to analyze
22:18:33.279 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeWebXmlFiles in component: WebXmlParser
22:18:33.281 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit5277120465433637905/web.xml
22:18:33.284 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit5277120465433637905/web.xml
22:18:33.287 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11655056075956336327/web.xml
22:18:33.288 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11655056075956336327/web.xml
22:18:33.291 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit12971958635389970998/web.xml
22:18:33.292 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit12971958635389970998/web.xml
22:18:33.295 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1163294882632423325/web.xml
22:18:33.297 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1163294882632423325/web.xml
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at Jul 9, 2025, 10:18:33 PM</p>
</div>
</div>
</body>
</html>
