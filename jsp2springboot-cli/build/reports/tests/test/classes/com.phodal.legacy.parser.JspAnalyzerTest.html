<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - JspAnalyzerTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>JspAnalyzerTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.phodal.legacy.parser.html">com.phodal.legacy.parser</a> &gt; JspAnalyzerTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">9</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.032s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
<li>
<a href="#">Standard output</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">testAnalyzeEmptyDirectory()</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeJspWithDeclarations()</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeJspWithErrorPage()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeJspWithImports()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeJspWithSessionDisabled()</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeJspxFile()</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeMultipleJspFiles()</td>
<td class="success">0.005s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeNonJspFiles()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeSimpleJspFile()</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>22:18:33.162 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeJspFiles in component: JspAnalyzer
22:18:33.163 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 1 JSP files to analyze
22:18:33.163 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 1/1 ({:.1f}%) - 100.0
22:18:33.163 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7275649911706173410/test.jspx
22:18:33.163 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7275649911706173410/test.jspx
22:18:33.164 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7275649911706173410/test.jspx
22:18:33.164 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7275649911706173410/test.jspx
22:18:33.164 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
22:18:33.167 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeJspFiles in component: JspAnalyzer
22:18:33.168 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 2 JSP files to analyze
22:18:33.168 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 1/2 ({:.1f}%) - 50.0
22:18:33.168 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1014400754885307052/page1.jsp
22:18:33.168 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1014400754885307052/page1.jsp
22:18:33.168 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1014400754885307052/page1.jsp
22:18:33.169 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1014400754885307052/page1.jsp
22:18:33.169 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 2/2 ({:.1f}%) - 100.0
22:18:33.169 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1014400754885307052/page2.jsp
22:18:33.169 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1014400754885307052/page2.jsp
22:18:33.169 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1014400754885307052/page2.jsp
22:18:33.170 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1014400754885307052/page2.jsp
22:18:33.170 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
22:18:33.173 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3072522269957620932/main.jsp
22:18:33.173 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3072522269957620932/main.jsp
22:18:33.173 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3072522269957620932/main.jsp
22:18:33.174 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3072522269957620932/main.jsp
22:18:33.177 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit8542164158013107428/nosession.jsp
22:18:33.177 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit8542164158013107428/nosession.jsp
22:18:33.177 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit8542164158013107428/nosession.jsp
22:18:33.178 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit8542164158013107428/nosession.jsp
22:18:33.182 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11541411582539781994/declarations.jsp
22:18:33.182 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11541411582539781994/declarations.jsp
22:18:33.183 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11541411582539781994/declarations.jsp
22:18:33.183 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11541411582539781994/declarations.jsp
22:18:33.187 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2688342717627467751/complex.jsp
22:18:33.187 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2688342717627467751/complex.jsp
22:18:33.187 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2688342717627467751/complex.jsp
22:18:33.188 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2688342717627467751/complex.jsp
22:18:33.191 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeJspFiles in component: JspAnalyzer
22:18:33.191 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 0 JSP files to analyze
22:18:33.191 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
22:18:33.194 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit12267091524417369597/test.jsp
22:18:33.194 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit12267091524417369597/test.jsp
22:18:33.194 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit12267091524417369597/test.jsp
22:18:33.195 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit12267091524417369597/test.jsp
22:18:33.198 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeJspFiles in component: JspAnalyzer
22:18:33.198 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 0 JSP files to analyze
22:18:33.198 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at Jul 9, 2025, 10:18:33 PM</p>
</div>
</div>
</body>
</html>
