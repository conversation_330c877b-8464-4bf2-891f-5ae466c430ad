<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - DependencyGraphTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>DependencyGraphTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.phodal.legacy.model.html">com.phodal.legacy.model</a> &gt; DependencyGraphTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">12</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.010s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
<li>
<a href="#">Standard output</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">testAddMultipleComponents()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAddSingleComponent()</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testBuildSimpleDependencies()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testCalculateDepths()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testCircularDependencyDetection()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testEmptyGraph()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGetNodesByType()</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGetRootAndLeafNodes()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGraphStatistics()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testMigrationOrder()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testNodeMigrationPriority()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testTopologicalSort()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>22:18:32.963 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 0 components
22:18:32.963 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
22:18:32.965 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 3 components
22:18:32.966 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
22:18:32.967 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 4 components
22:18:32.967 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
22:18:32.969 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 5 components
22:18:32.969 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
22:18:32.970 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 2 components
22:18:32.971 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
22:18:32.973 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 3 components
22:18:32.973 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
22:18:32.974 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 3 components
22:18:32.975 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
22:18:32.977 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 4 components
22:18:32.977 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
22:18:32.979 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 3 components
22:18:32.980 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at Jul 9, 2025, 10:18:33 PM</p>
</div>
</div>
</body>
</html>
