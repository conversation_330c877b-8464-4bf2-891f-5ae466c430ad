@rem
@rem Copyright 2015 the original author or authors.
@rem
@rem Licensed under the Apache License, Version 2.0 (the "License");
@rem you may not use this file except in compliance with the License.
@rem You may obtain a copy of the License at
@rem
@rem      https://www.apache.org/licenses/LICENSE-2.0
@rem
@rem Unless required by applicable law or agreed to in writing, software
@rem distributed under the License is distributed on an "AS IS" BASIS,
@rem WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
@rem See the License for the specific language governing permissions and
@rem limitations under the License.
@rem
@rem SPDX-License-Identifier: Apache-2.0
@rem

@if "%DEBUG%"=="" @echo off
@rem ##########################################################################
@rem
@rem  jsp2springboot-cli startup script for Windows
@rem
@rem ##########################################################################

@rem Set local scope for the variables with windows NT shell
if "%OS%"=="Windows_NT" setlocal

set DIRNAME=%~dp0
if "%DIRNAME%"=="" set DIRNAME=.
@rem This is normally unused
set APP_BASE_NAME=%~n0
set APP_HOME=%DIRNAME%..

@rem Resolve any "." and ".." in APP_HOME to make it shorter.
for %%i in ("%APP_HOME%") do set APP_HOME=%%~fi

@rem Add default JVM options here. You can also use JAVA_OPTS and JSP2SPRINGBOOT_CLI_OPTS to pass JVM options to this script.
set DEFAULT_JVM_OPTS=

@rem Find java.exe
if defined JAVA_HOME goto findJavaFromJavaHome

set JAVA_EXE=java.exe
%JAVA_EXE% -version >NUL 2>&1
if %ERRORLEVEL% equ 0 goto execute

echo. 1>&2
echo ERROR: JAVA_HOME is not set and no 'java' command could be found in your PATH. 1>&2
echo. 1>&2
echo Please set the JAVA_HOME variable in your environment to match the 1>&2
echo location of your Java installation. 1>&2

goto fail

:findJavaFromJavaHome
set JAVA_HOME=%JAVA_HOME:"=%
set JAVA_EXE=%JAVA_HOME%/bin/java.exe

if exist "%JAVA_EXE%" goto execute

echo. 1>&2
echo ERROR: JAVA_HOME is set to an invalid directory: %JAVA_HOME% 1>&2
echo. 1>&2
echo Please set the JAVA_HOME variable in your environment to match the 1>&2
echo location of your Java installation. 1>&2

goto fail

:execute
@rem Setup the command line

set CLASSPATH=%APP_HOME%\lib\jsp2springboot-cli-1.0.0-plain.jar;%APP_HOME%\lib\picocli-4.7.5.jar;%APP_HOME%\lib\javapoet-1.13.0.jar;%APP_HOME%\lib\freemarker-2.3.32.jar;%APP_HOME%\lib\javaparser-symbol-solver-core-3.25.7.jar;%APP_HOME%\lib\javaparser-core-3.25.7.jar;%APP_HOME%\lib\rewrite-gradle-8.21.0.jar;%APP_HOME%\lib\rewrite-groovy-8.21.0.jar;%APP_HOME%\lib\rewrite-maven-8.21.0.jar;%APP_HOME%\lib\rewrite-java-8.21.0.jar;%APP_HOME%\lib\rewrite-xml-8.21.0.jar;%APP_HOME%\lib\rewrite-yaml-8.21.0.jar;%APP_HOME%\lib\rewrite-properties-8.21.0.jar;%APP_HOME%\lib\rewrite-core-8.21.0.jar;%APP_HOME%\lib\gizmo-1.0.11.Final.jar;%APP_HOME%\lib\asm-util-9.6.jar;%APP_HOME%\lib\asm-analysis-9.6.jar;%APP_HOME%\lib\asm-tree-9.6.jar;%APP_HOME%\lib\asm-9.6.jar;%APP_HOME%\lib\tomcat-jasper-10.1.17.jar;%APP_HOME%\lib\tomcat-jsp-api-10.1.17.jar;%APP_HOME%\lib\jsoup-1.17.2.jar;%APP_HOME%\lib\dom4j-2.1.4.jar;%APP_HOME%\lib\jaxen-2.0.0.jar;%APP_HOME%\lib\spring-boot-starter-web-3.2.1.jar;%APP_HOME%\lib\spring-boot-starter-json-3.2.1.jar;%APP_HOME%\lib\spring-boot-starter-3.2.1.jar;%APP_HOME%\lib\spring-boot-starter-logging-3.2.1.jar;%APP_HOME%\lib\commons-text-1.11.0.jar;%APP_HOME%\lib\commons-compress-1.26.1.jar;%APP_HOME%\lib\commons-lang3-3.14.0.jar;%APP_HOME%\lib\commons-io-2.15.1.jar;%APP_HOME%\lib\guava-32.1.3-jre.jar;%APP_HOME%\lib\jackson-datatype-jsr310-2.15.3.jar;%APP_HOME%\lib\jackson-dataformat-xml-2.15.3.jar;%APP_HOME%\lib\jackson-module-jaxb-annotations-2.15.3.jar;%APP_HOME%\lib\jackson-annotations-2.15.3.jar;%APP_HOME%\lib\jackson-dataformat-smile-2.15.3.jar;%APP_HOME%\lib\jackson-module-parameter-names-2.15.3.jar;%APP_HOME%\lib\jackson-datatype-jdk8-2.15.3.jar;%APP_HOME%\lib\jackson-core-2.15.3.jar;%APP_HOME%\lib\jackson-dataformat-yaml-2.16.1.jar;%APP_HOME%\lib\jackson-databind-2.16.1.jar;%APP_HOME%\lib\logback-classic-1.4.14.jar;%APP_HOME%\lib\log4j-to-slf4j-2.21.1.jar;%APP_HOME%\lib\jul-to-slf4j-2.0.9.jar;%APP_HOME%\lib\slf4j-api-2.0.9.jar;%APP_HOME%\lib\javassist-3.29.2-GA.jar;%APP_HOME%\lib\tomcat-util-scan-10.1.17.jar;%APP_HOME%\lib\tomcat-api-10.1.17.jar;%APP_HOME%\lib\tomcat-servlet-api-10.1.17.jar;%APP_HOME%\lib\tomcat-util-10.1.17.jar;%APP_HOME%\lib\tomcat-juli-10.1.17.jar;%APP_HOME%\lib\tomcat-jasper-el-10.1.17.jar;%APP_HOME%\lib\tomcat-el-api-10.1.17.jar;%APP_HOME%\lib\ecj-3.33.0.jar;%APP_HOME%\lib\stax-api-1.0-2.jar;%APP_HOME%\lib\xsdlib-2013.6.1.jar;%APP_HOME%\lib\jaxb-api-2.2.12.jar;%APP_HOME%\lib\pull-parser-2.1.10.jar;%APP_HOME%\lib\xpp3-1.1.4c.jar;%APP_HOME%\lib\micrometer-core-1.12.1.jar;%APP_HOME%\lib\java-object-diff-1.0.1.jar;%APP_HOME%\lib\annotations-24.1.0.jar;%APP_HOME%\lib\antlr4-runtime-4.11.1.jar;%APP_HOME%\lib\classgraph-4.8.168.jar;%APP_HOME%\lib\snappy-java-1.1.10.5.jar;%APP_HOME%\lib\fastfilter-1.0.2.jar;%APP_HOME%\lib\spring-boot-autoconfigure-3.2.1.jar;%APP_HOME%\lib\spring-boot-3.2.1.jar;%APP_HOME%\lib\spring-boot-starter-tomcat-3.2.1.jar;%APP_HOME%\lib\jakarta.annotation-api-2.1.1.jar;%APP_HOME%\lib\spring-webmvc-6.1.2.jar;%APP_HOME%\lib\spring-web-6.1.2.jar;%APP_HOME%\lib\spring-context-6.1.2.jar;%APP_HOME%\lib\spring-aop-6.1.2.jar;%APP_HOME%\lib\spring-beans-6.1.2.jar;%APP_HOME%\lib\spring-expression-6.1.2.jar;%APP_HOME%\lib\spring-core-6.1.2.jar;%APP_HOME%\lib\snakeyaml-2.2.jar;%APP_HOME%\lib\failureaccess-1.0.1.jar;%APP_HOME%\lib\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;%APP_HOME%\lib\jsr305-3.0.2.jar;%APP_HOME%\lib\caffeine-3.1.8.jar;%APP_HOME%\lib\checker-qual-3.37.0.jar;%APP_HOME%\lib\error_prone_annotations-2.21.1.jar;%APP_HOME%\lib\logback-core-1.4.14.jar;%APP_HOME%\lib\relaxngDatatype-20020414.jar;%APP_HOME%\lib\jna-platform-5.14.0.jar;%APP_HOME%\lib\micrometer-observation-1.12.1.jar;%APP_HOME%\lib\micrometer-commons-1.12.1.jar;%APP_HOME%\lib\HdrHistogram-2.1.12.jar;%APP_HOME%\lib\LatencyUtils-2.0.3.jar;%APP_HOME%\lib\failsafe-3.3.2.jar;%APP_HOME%\lib\spring-jcl-6.1.2.jar;%APP_HOME%\lib\tomcat-embed-websocket-10.1.17.jar;%APP_HOME%\lib\tomcat-embed-core-10.1.17.jar;%APP_HOME%\lib\tomcat-embed-el-10.1.17.jar;%APP_HOME%\lib\log4j-api-2.21.1.jar;%APP_HOME%\lib\jandex-2.4.2.Final.jar;%APP_HOME%\lib\jna-5.14.0.jar;%APP_HOME%\lib\commons-codec-1.16.0.jar;%APP_HOME%\lib\woodstox-core-6.5.1.jar;%APP_HOME%\lib\stax2-api-4.2.1.jar;%APP_HOME%\lib\jakarta.xml.bind-api-4.0.1.jar;%APP_HOME%\lib\jakarta.activation-api-2.1.2.jar


@rem Execute jsp2springboot-cli
"%JAVA_EXE%" %DEFAULT_JVM_OPTS% %JAVA_OPTS% %JSP2SPRINGBOOT_CLI_OPTS%  -classpath "%CLASSPATH%" com.phodal.legacy.CliApp %*

:end
@rem End local scope for the variables with windows NT shell
if %ERRORLEVEL% equ 0 goto mainEnd

:fail
rem Set variable JSP2SPRINGBOOT_CLI_EXIT_CONSOLE if you need the _script_ return code instead of
rem the _cmd.exe /c_ return code!
set EXIT_CODE=%ERRORLEVEL%
if %EXIT_CODE% equ 0 set EXIT_CODE=1
if not ""=="%JSP2SPRINGBOOT_CLI_EXIT_CONSOLE%" exit %EXIT_CODE%
exit /b %EXIT_CODE%

:mainEnd
if "%OS%"=="Windows_NT" endlocal

:omega
