# JSP to Spring Boot Migration CLI

A Java CLI application for automatically converting legacy JSP projects to Spring Boot applications.

## Features

- **Analyze**: Examine legacy JSP project structure and dependencies
- **Convert**: Transform JSP projects into modern Spring Boot applications  
- **Validate**: Verify the converted Spring Boot project

## Technology Stack

- **Build System**: Gradle
- **CLI Framework**: Pico<PERSON>li
- **Code Generation**: JavaPoet, Freemarker
- **Code Analysis**: JavaParser, ASM, JSoup
- **Migration**: OpenRewrite
- **Base Package**: `com.phodal.legacy`

## Quick Start

### Prerequisites

- Java 17 or higher
- Gradle (or use the included wrapper)

### Build the Project

```bash
./gradlew build
```

### Run the CLI

```bash
# Show help
./gradlew run --args="--help"

# Analyze a legacy JSP project
./gradlew run --args="analyze /path/to/legacy/project"

# Convert a project
./gradlew run --args="convert /path/to/legacy/project /path/to/output/project"

# Validate a converted project
./gradlew run --args="validate /path/to/converted/project"
```

### Create Executable JAR

```bash
./gradlew fatJar
```

The executable JAR will be created at `build/libs/jsp2springboot-cli-1.0.0-all.jar`

## Commands

### analyze

Analyze legacy JSP project structure and dependencies.

```bash
./gradlew run --args="analyze [OPTIONS] <project-path>"
```

**Options:**
- `--include-jsp=<boolean>` - Include JSP file analysis (default: true)
- `--include-java=<boolean>` - Include Java source analysis (default: true)  
- `--include-web-xml=<boolean>` - Include web.xml analysis (default: true)
- `-o, --output=<file>` - Output file for analysis report

### convert

Convert legacy JSP project to Spring Boot application.

```bash
./gradlew run --args="convert [OPTIONS] <source-path> <target-path>"
```

**Options:**
- `--spring-boot-version=<version>` - Target Spring Boot version (default: 3.2.1)
- `--java-version=<version>` - Target Java version (default: 17)
- `--package-name=<name>` - Base package name for generated code (default: com.example.migrated)

### validate

Validate converted Spring Boot project.

```bash
./gradlew run --args="validate [OPTIONS] <project-path>"
```

**Options:**
- `--build-test=<boolean>` - Run build test (default: true)
- `--unit-test=<boolean>` - Run unit tests (default: true)

## Project Structure

```
jsp2springboot-cli/
├── build.gradle                    # Gradle build configuration
├── settings.gradle                 # Gradle settings
├── src/
│   ├── main/
│   │   └── java/
│   │       └── com/phodal/legacy/
│   │           ├── CliApp.java                # Main CLI entry point
│   │           ├── config/                    # Configuration classes
│   │           ├── model/                     # Data models
│   │           ├── utils/                     # Utility classes
│   │           ├── parser/                    # Code parsers
│   │           ├── generator/                 # Code generators
│   │           ├── ai/                        # AI integration
│   │           ├── services/                  # Business services
│   │           ├── agent/                     # AI agents
│   │           ├── tool/                      # AI tools
│   │           └── mcp/                       # MCP protocol
│   └── test/
│       └── java/                              # JUnit tests
└── README.md                                  # This file
```

## Development

### Running Tests

```bash
./gradlew test
```

### Code Quality

The project includes comprehensive logging and error handling to provide useful feedback during migration operations.

### Extending the Tool

The modular architecture allows easy extension:

- Add new parsers in the `parser` package
- Implement custom generators in the `generator` package  
- Create AI agents in the `agent` package
- Add tools in the `tool` package

## License

This project is part of the autodev-legacy toolkit for modernizing legacy Java applications.
