package com.phodal.legacy.model;

import java.util.*;

/**
 * Represents a component in the legacy JSP application that needs to be migrated.
 * This could be a JSP page, Java class, servlet, or other application component.
 */
public class ComponentModel {
    
    private String id;
    private String name;
    private ComponentType type;
    private String sourcePath;
    private String targetPath;
    private Map<String, Object> properties;
    private Set<String> dependencies;
    private Set<String> dependents;
    private MigrationStatus status;
    private List<String> migrationNotes;
    
    public ComponentModel(String id, String name, ComponentType type) {
        this.id = id;
        this.name = name;
        this.type = type;
        this.properties = new HashMap<>();
        this.dependencies = new HashSet<>();
        this.dependents = new HashSet<>();
        this.status = MigrationStatus.NOT_STARTED;
        this.migrationNotes = new ArrayList<>();
    }
    
    // Getters and setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public ComponentType getType() { return type; }
    public void setType(ComponentType type) { this.type = type; }
    
    public String getSourcePath() { return sourcePath; }
    public void setSourcePath(String sourcePath) { this.sourcePath = sourcePath; }
    
    public String getTargetPath() { return targetPath; }
    public void setTargetPath(String targetPath) { this.targetPath = targetPath; }
    
    public Map<String, Object> getProperties() { return properties; }
    public void setProperties(Map<String, Object> properties) { this.properties = properties; }
    
    public Set<String> getDependencies() { return dependencies; }
    public void setDependencies(Set<String> dependencies) { this.dependencies = dependencies; }
    
    public Set<String> getDependents() { return dependents; }
    public void setDependents(Set<String> dependents) { this.dependents = dependents; }
    
    public MigrationStatus getStatus() { return status; }
    public void setStatus(MigrationStatus status) { this.status = status; }
    
    public List<String> getMigrationNotes() { return migrationNotes; }
    public void setMigrationNotes(List<String> migrationNotes) { this.migrationNotes = migrationNotes; }
    
    // Utility methods
    public void addProperty(String key, Object value) {
        this.properties.put(key, value);
    }
    
    public Object getProperty(String key) {
        return this.properties.get(key);
    }
    
    public void addDependency(String componentId) {
        this.dependencies.add(componentId);
    }
    
    public void addDependent(String componentId) {
        this.dependents.add(componentId);
    }
    
    public void addMigrationNote(String note) {
        this.migrationNotes.add(note);
    }
    
    public boolean hasDependencies() {
        return !dependencies.isEmpty();
    }
    
    public boolean hasDependents() {
        return !dependents.isEmpty();
    }
    
    @Override
    public String toString() {
        return String.format("ComponentModel{id='%s', name='%s', type=%s, status=%s}", 
                           id, name, type, status);
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ComponentModel that = (ComponentModel) o;
        return Objects.equals(id, that.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    /**
     * Component types in a legacy JSP application
     */
    public enum ComponentType {
        JSP_PAGE,
        SERVLET,
        JAVA_CLASS,
        FILTER,
        LISTENER,
        TAG_LIBRARY,
        WEB_XML,
        CONFIGURATION,
        STATIC_RESOURCE,
        DEPENDENCY_JAR,
        UNKNOWN
    }
    
    /**
     * Migration status for tracking progress
     */
    public enum MigrationStatus {
        NOT_STARTED,
        ANALYZING,
        ANALYZED,
        MIGRATING,
        MIGRATED,
        TESTING,
        COMPLETED,
        FAILED,
        SKIPPED
    }
}
