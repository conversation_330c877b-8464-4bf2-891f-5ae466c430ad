package com.phodal.legacy.parser;

import com.github.javaparser.JavaParser;
import com.github.javaparser.ParseResult;
import com.github.javaparser.ast.CompilationUnit;
import com.github.javaparser.ast.ImportDeclaration;
import com.github.javaparser.ast.body.*;
import com.github.javaparser.ast.expr.AnnotationExpr;
import com.github.javaparser.ast.type.ClassOrInterfaceType;
import com.github.javaparser.ast.visitor.VoidVisitorAdapter;
import com.phodal.legacy.model.ComponentModel;
import com.phodal.legacy.utils.FileUtils;
import com.phodal.legacy.utils.LoggingUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.stream.Stream;

/**
 * Analyzer for Java source code files to extract class information, dependencies, and structure.
 * Uses JavaParser to analyze Java files and identify Spring Boot migration requirements.
 */
public class JavaCodeAnalyzer {
    
    private static final Logger logger = LoggerFactory.getLogger(JavaCodeAnalyzer.class);
    private final JavaParser javaParser;
    
    public JavaCodeAnalyzer() {
        this.javaParser = new JavaParser();
    }
    
    /**
     * Analyze all Java files in the given directory
     */
    public List<ComponentModel> analyzeJavaFiles(Path projectRoot) throws IOException {
        LoggingUtils.logOperationStart("analyzeJavaFiles", "JavaCodeAnalyzer");
        
        List<ComponentModel> javaComponents = new ArrayList<>();
        
        try (Stream<Path> paths = Files.walk(projectRoot)) {
            List<Path> javaFiles = paths
                .filter(Files::isRegularFile)
                .filter(path -> isJavaFile(path))
                .toList();
            
            logger.info("Found {} Java files to analyze", javaFiles.size());
            
            for (int i = 0; i < javaFiles.size(); i++) {
                Path javaFile = javaFiles.get(i);
                LoggingUtils.logProgress(i + 1, javaFiles.size(), "Analyzing Java: " + javaFile.getFileName());
                
                try {
                    ComponentModel component = analyzeJavaFile(javaFile, projectRoot);
                    if (component != null) {
                        javaComponents.add(component);
                    }
                } catch (Exception e) {
                    logger.warn("Failed to analyze Java file: {} - {}", javaFile, e.getMessage());
                }
            }
            
            LoggingUtils.logOperationComplete("analyzeJavaFiles", "JavaCodeAnalyzer");
            return javaComponents;
            
        } catch (IOException e) {
            LoggingUtils.logOperationError("analyzeJavaFiles", "JavaCodeAnalyzer", e);
            throw e;
        }
    }
    
    /**
     * Analyze a single Java file
     */
    public ComponentModel analyzeJavaFile(Path javaFile, Path projectRoot) throws IOException {
        LoggingUtils.logFileProcessing(javaFile.toString(), "Analyzing Java");
        
        String content = FileUtils.readFileContent(javaFile);
        String relativePath = FileUtils.getRelativePath(projectRoot, javaFile);
        
        ParseResult<CompilationUnit> parseResult = javaParser.parse(content);
        
        if (!parseResult.isSuccessful()) {
            logger.warn("Failed to parse Java file: {} - {}", javaFile, parseResult.getProblems());
            return null;
        }
        
        CompilationUnit cu = parseResult.getResult().orElse(null);
        if (cu == null) {
            return null;
        }
        
        // Determine component type based on class characteristics
        ComponentModel.ComponentType componentType = determineComponentType(cu);

        ComponentModel component = new ComponentModel(
            relativePath,
            javaFile.getFileName().toString(),
            componentType
        );
        component.setSourcePath(javaFile.toString());

        // Extract Java metadata
        JavaMetadata metadata = extractJavaMetadata(cu);
        component.getProperties().putAll(metadata.toMap());

        // Extract dependencies
        Set<String> dependencies = extractDependencies(cu);
        component.getDependencies().addAll(dependencies);

        // Extract annotations
        Set<String> annotations = extractAnnotations(cu);
        component.getProperties().put("annotations", new ArrayList<>(annotations));

        // Calculate complexity
        int complexity = calculateComplexity(cu);
        component.getProperties().put("complexity", complexity);
        
        LoggingUtils.logFileProcessed(javaFile.toString(), "Analyzed Java");
        return component;
    }
    
    /**
     * Check if a file is a Java file
     */
    private boolean isJavaFile(Path path) {
        return path.getFileName().toString().toLowerCase().endsWith(".java");
    }
    
    /**
     * Determine component type based on class characteristics
     */
    private ComponentModel.ComponentType determineComponentType(CompilationUnit cu) {
        JavaClassVisitor visitor = new JavaClassVisitor();
        visitor.visit(cu, null);
        
        if (visitor.isServlet()) {
            return ComponentModel.ComponentType.SERVLET;
        } else if (visitor.isFilter()) {
            return ComponentModel.ComponentType.FILTER;
        } else if (visitor.isListener()) {
            return ComponentModel.ComponentType.LISTENER;
        } else {
            return ComponentModel.ComponentType.JAVA_CLASS;
        }
    }
    
    /**
     * Extract Java metadata from compilation unit
     */
    private JavaMetadata extractJavaMetadata(CompilationUnit cu) {
        JavaMetadata metadata = new JavaMetadata();
        
        // Extract package
        cu.getPackageDeclaration().ifPresent(pkg -> 
            metadata.setPackageName(pkg.getNameAsString()));
        
        // Extract class information
        cu.findAll(ClassOrInterfaceDeclaration.class).forEach(cls -> {
            if (cls.isInterface()) {
                metadata.addInterface(cls.getNameAsString());
            } else {
                metadata.addClass(cls.getNameAsString());
                
                // Extract extends and implements
                cls.getExtendedTypes().forEach(ext -> 
                    metadata.addSuperClass(ext.getNameAsString()));
                cls.getImplementedTypes().forEach(impl -> 
                    metadata.addImplementedInterface(impl.getNameAsString()));
            }
        });
        
        // Extract enum information
        cu.findAll(EnumDeclaration.class).forEach(enumDecl -> 
            metadata.addEnum(enumDecl.getNameAsString()));
        
        return metadata;
    }
    
    /**
     * Extract dependencies from compilation unit
     */
    private Set<String> extractDependencies(CompilationUnit cu) {
        Set<String> dependencies = new HashSet<>();
        
        // Extract from imports
        cu.getImports().forEach(imp -> {
            if (!imp.isAsterisk()) {
                dependencies.add(imp.getNameAsString());
            } else {
                // For wildcard imports, add the package
                dependencies.add(imp.getNameAsString() + ".*");
            }
        });
        
        // Extract from type usage
        cu.findAll(ClassOrInterfaceType.class).forEach(type -> {
            String typeName = type.getNameAsString();
            if (isFullyQualifiedName(typeName)) {
                dependencies.add(typeName);
            }
        });
        
        return dependencies;
    }
    
    /**
     * Extract annotations from compilation unit
     */
    private Set<String> extractAnnotations(CompilationUnit cu) {
        Set<String> annotations = new HashSet<>();
        
        cu.findAll(AnnotationExpr.class).forEach(annotation -> 
            annotations.add(annotation.getNameAsString()));
        
        return annotations;
    }
    
    /**
     * Calculate complexity score for Java file
     */
    private int calculateComplexity(CompilationUnit cu) {
        ComplexityCalculator calculator = new ComplexityCalculator();
        calculator.visit(cu, null);
        return calculator.getComplexity();
    }
    
    /**
     * Check if a string is a fully qualified class name
     */
    private boolean isFullyQualifiedName(String name) {
        return name.contains(".") && Character.isUpperCase(name.charAt(name.lastIndexOf('.') + 1));
    }
    
    /**
     * Visitor to identify class characteristics
     */
    private static class JavaClassVisitor extends VoidVisitorAdapter<Void> {
        private boolean isServlet = false;
        private boolean isFilter = false;
        private boolean isListener = false;
        
        @Override
        public void visit(ClassOrInterfaceDeclaration n, Void arg) {
            super.visit(n, arg);
            
            // Check extends
            n.getExtendedTypes().forEach(ext -> {
                String extName = ext.getNameAsString();
                if (extName.contains("HttpServlet") || extName.contains("GenericServlet")) {
                    isServlet = true;
                }
            });
            
            // Check implements
            n.getImplementedTypes().forEach(impl -> {
                String implName = impl.getNameAsString();
                if (implName.contains("Filter")) {
                    isFilter = true;
                } else if (implName.contains("Listener")) {
                    isListener = true;
                }
            });
            
            // Check annotations
            n.getAnnotations().forEach(annotation -> {
                String annotationName = annotation.getNameAsString();
                if (annotationName.equals("WebServlet")) {
                    isServlet = true;
                } else if (annotationName.equals("WebFilter")) {
                    isFilter = true;
                } else if (annotationName.equals("WebListener")) {
                    isListener = true;
                }
            });
        }
        
        public boolean isServlet() { return isServlet; }
        public boolean isFilter() { return isFilter; }
        public boolean isListener() { return isListener; }
    }
    
    /**
     * Visitor to calculate code complexity
     */
    private static class ComplexityCalculator extends VoidVisitorAdapter<Void> {
        private int complexity = 0;
        
        @Override
        public void visit(MethodDeclaration n, Void arg) {
            super.visit(n, arg);
            complexity += 1; // Each method adds to complexity
        }
        
        @Override
        public void visit(ConstructorDeclaration n, Void arg) {
            super.visit(n, arg);
            complexity += 1; // Each constructor adds to complexity
        }
        
        @Override
        public void visit(ClassOrInterfaceDeclaration n, Void arg) {
            super.visit(n, arg);
            complexity += 1; // Each class/interface adds to complexity
        }
        
        public int getComplexity() { return complexity; }
    }
    
    /**
     * Java metadata container
     */
    public static class JavaMetadata {
        private String packageName;
        private Set<String> classes = new HashSet<>();
        private Set<String> interfaces = new HashSet<>();
        private Set<String> enums = new HashSet<>();
        private Set<String> superClasses = new HashSet<>();
        private Set<String> implementedInterfaces = new HashSet<>();
        
        // Getters and setters
        public String getPackageName() { return packageName; }
        public void setPackageName(String packageName) { this.packageName = packageName; }
        
        public Set<String> getClasses() { return classes; }
        public void addClass(String className) { this.classes.add(className); }
        
        public Set<String> getInterfaces() { return interfaces; }
        public void addInterface(String interfaceName) { this.interfaces.add(interfaceName); }
        
        public Set<String> getEnums() { return enums; }
        public void addEnum(String enumName) { this.enums.add(enumName); }
        
        public Set<String> getSuperClasses() { return superClasses; }
        public void addSuperClass(String superClass) { this.superClasses.add(superClass); }
        
        public Set<String> getImplementedInterfaces() { return implementedInterfaces; }
        public void addImplementedInterface(String interfaceName) { this.implementedInterfaces.add(interfaceName); }
        
        public Map<String, Object> toMap() {
            Map<String, Object> map = new HashMap<>();
            if (packageName != null) map.put("packageName", packageName);
            map.put("classes", new ArrayList<>(classes));
            map.put("interfaces", new ArrayList<>(interfaces));
            map.put("enums", new ArrayList<>(enums));
            map.put("superClasses", new ArrayList<>(superClasses));
            map.put("implementedInterfaces", new ArrayList<>(implementedInterfaces));
            return map;
        }
    }
}
