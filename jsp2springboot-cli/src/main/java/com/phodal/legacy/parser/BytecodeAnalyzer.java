package com.phodal.legacy.parser;

import com.phodal.legacy.model.ComponentModel;
import com.phodal.legacy.model.JspBytecodeMapping;
import com.phodal.legacy.model.MappingRegistry;
import com.phodal.legacy.utils.FileUtils;
import com.phodal.legacy.utils.LoggingUtils;
import org.objectweb.asm.*;
import org.objectweb.asm.tree.ClassNode;
import org.objectweb.asm.tree.FieldNode;
import org.objectweb.asm.tree.MethodNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;
import java.util.stream.Stream;

/**
 * Analyzer for compiled bytecode (JAR files and class files) to extract runtime dependencies
 * and class information. Uses ASM to analyze bytecode and identify Spring Boot migration requirements.
 */
public class BytecodeAnalyzer {
    
    private static final Logger logger = LoggerFactory.getLogger(BytecodeAnalyzer.class);
    
    /**
     * Analyze all JAR and class files in the given directory
     */
    public List<ComponentModel> analyzeBytecode(Path projectRoot) throws IOException {
        LoggingUtils.logOperationStart("analyzeBytecode", "BytecodeAnalyzer");
        
        List<ComponentModel> bytecodeComponents = new ArrayList<>();
        
        try (Stream<Path> paths = Files.walk(projectRoot)) {
            List<Path> bytecodeFiles = paths
                .filter(Files::isRegularFile)
                .filter(path -> isBytecodeFile(path))
                .toList();
            
            logger.info("Found {} bytecode files to analyze", bytecodeFiles.size());
            
            for (int i = 0; i < bytecodeFiles.size(); i++) {
                Path bytecodeFile = bytecodeFiles.get(i);
                LoggingUtils.logProgress(i + 1, bytecodeFiles.size(), 
                    "Analyzing bytecode: " + bytecodeFile.getFileName());
                
                try {
                    List<ComponentModel> components = analyzeBytecodeFile(bytecodeFile, projectRoot);
                    bytecodeComponents.addAll(components);
                } catch (Exception e) {
                    logger.warn("Failed to analyze bytecode file: {} - {}", bytecodeFile, e.getMessage());
                }
            }
            
            LoggingUtils.logOperationComplete("analyzeBytecode", "BytecodeAnalyzer");
            return bytecodeComponents;
            
        } catch (IOException e) {
            LoggingUtils.logOperationError("analyzeBytecode", "BytecodeAnalyzer", e);
            throw e;
        }
    }
    
    /**
     * Analyze a single bytecode file (JAR or class)
     */
    public List<ComponentModel> analyzeBytecodeFile(Path bytecodeFile, Path projectRoot) throws IOException {
        LoggingUtils.logFileProcessing(bytecodeFile.toString(), "Analyzing bytecode");
        
        List<ComponentModel> components = new ArrayList<>();
        String relativePath = FileUtils.getRelativePath(projectRoot, bytecodeFile);
        
        if (bytecodeFile.toString().toLowerCase().endsWith(".jar")) {
            components.addAll(analyzeJarFile(bytecodeFile, relativePath));
        } else if (bytecodeFile.toString().toLowerCase().endsWith(".class")) {
            ComponentModel component = analyzeClassFile(bytecodeFile, relativePath);
            if (component != null) {
                components.add(component);
            }
        }
        
        LoggingUtils.logFileProcessed(bytecodeFile.toString(), "Analyzed bytecode");
        return components;
    }
    
    /**
     * Analyze a JAR file
     */
    private List<ComponentModel> analyzeJarFile(Path jarFile, String relativePath) throws IOException {
        List<ComponentModel> components = new ArrayList<>();
        
        try (JarFile jar = new JarFile(jarFile.toFile())) {
            Enumeration<JarEntry> entries = jar.entries();
            
            while (entries.hasMoreElements()) {
                JarEntry entry = entries.nextElement();
                
                if (entry.getName().endsWith(".class") && !entry.isDirectory()) {
                    try (InputStream is = jar.getInputStream(entry)) {
                        ComponentModel component = analyzeClassInputStream(is, 
                            relativePath + "!" + entry.getName());
                        if (component != null) {
                            components.add(component);
                        }
                    } catch (Exception e) {
                        logger.debug("Failed to analyze class in JAR: {} - {}", 
                            entry.getName(), e.getMessage());
                    }
                }
            }
        }
        
        return components;
    }
    
    /**
     * Analyze a single class file
     */
    private ComponentModel analyzeClassFile(Path classFile, String relativePath) throws IOException {
        try (InputStream is = Files.newInputStream(classFile)) {
            return analyzeClassInputStream(is, relativePath);
        }
    }
    
    /**
     * Analyze class from input stream
     */
    private ComponentModel analyzeClassInputStream(InputStream is, String path) throws IOException {
        ClassReader classReader = new ClassReader(is);
        ClassNode classNode = new ClassNode();
        classReader.accept(classNode, ClassReader.SKIP_DEBUG);
        
        // Determine component type
        ComponentModel.ComponentType componentType = determineComponentType(classNode);

        String className = classNode.name.substring(classNode.name.lastIndexOf('/') + 1);
        ComponentModel component = new ComponentModel(
            path,
            className,
            componentType
        );
        component.setSourcePath(path);

        // Extract bytecode metadata
        BytecodeMetadata metadata = extractBytecodeMetadata(classNode);
        component.getProperties().putAll(metadata.toMap());

        // Extract dependencies
        Set<String> dependencies = extractDependencies(classNode);
        component.getDependencies().addAll(dependencies);

        // Extract annotations
        Set<String> annotations = extractAnnotations(classNode);
        component.getProperties().put("annotations", new ArrayList<>(annotations));
        
        return component;
    }
    
    /**
     * Check if a file is a bytecode file
     */
    private boolean isBytecodeFile(Path path) {
        String fileName = path.getFileName().toString().toLowerCase();
        return fileName.endsWith(".jar") || fileName.endsWith(".class");
    }
    
    /**
     * Determine component type based on class characteristics
     */
    private ComponentModel.ComponentType determineComponentType(ClassNode classNode) {
        // Check superclass
        if (classNode.superName != null) {
            String superName = classNode.superName.replace('/', '.');
            if (superName.contains("HttpServlet") || superName.contains("GenericServlet")) {
                return ComponentModel.ComponentType.SERVLET;
            }
        }
        
        // Check interfaces
        if (classNode.interfaces != null) {
            for (String interfaceName : classNode.interfaces) {
                String intfName = interfaceName.replace('/', '.');
                if (intfName.contains("Filter")) {
                    return ComponentModel.ComponentType.FILTER;
                } else if (intfName.contains("Listener")) {
                    return ComponentModel.ComponentType.LISTENER;
                }
            }
        }
        
        // Check annotations
        if (classNode.visibleAnnotations != null) {
            for (org.objectweb.asm.tree.AnnotationNode annotation : classNode.visibleAnnotations) {
                String annotationType = Type.getType(annotation.desc).getClassName();
                if (annotationType.contains("WebServlet")) {
                    return ComponentModel.ComponentType.SERVLET;
                } else if (annotationType.contains("WebFilter")) {
                    return ComponentModel.ComponentType.FILTER;
                } else if (annotationType.contains("WebListener")) {
                    return ComponentModel.ComponentType.LISTENER;
                }
            }
        }
        
        return ComponentModel.ComponentType.JAVA_CLASS;
    }
    
    /**
     * Extract bytecode metadata
     */
    private BytecodeMetadata extractBytecodeMetadata(ClassNode classNode) {
        BytecodeMetadata metadata = new BytecodeMetadata();
        
        // Basic class information
        metadata.setClassName(classNode.name.replace('/', '.'));
        metadata.setAccess(classNode.access);
        metadata.setVersion(classNode.version);
        
        if (classNode.superName != null) {
            metadata.setSuperClass(classNode.superName.replace('/', '.'));
        }
        
        // Interfaces
        if (classNode.interfaces != null) {
            for (String interfaceName : classNode.interfaces) {
                metadata.addInterface(interfaceName.replace('/', '.'));
            }
        }
        
        // Methods
        if (classNode.methods != null) {
            for (MethodNode method : classNode.methods) {
                metadata.addMethod(method.name);
            }
        }
        
        // Fields
        if (classNode.fields != null) {
            for (FieldNode field : classNode.fields) {
                metadata.addField(field.name);
            }
        }
        
        return metadata;
    }
    
    /**
     * Extract dependencies from bytecode
     */
    private Set<String> extractDependencies(ClassNode classNode) {
        Set<String> dependencies = new HashSet<>();
        
        // Add superclass
        if (classNode.superName != null) {
            dependencies.add(classNode.superName.replace('/', '.'));
        }
        
        // Add interfaces
        if (classNode.interfaces != null) {
            for (String interfaceName : classNode.interfaces) {
                dependencies.add(interfaceName.replace('/', '.'));
            }
        }
        
        // Extract from method signatures and field types
        DependencyExtractor extractor = new DependencyExtractor(dependencies);
        classNode.accept(extractor);
        
        return dependencies;
    }
    
    /**
     * Extract annotations from bytecode
     */
    private Set<String> extractAnnotations(ClassNode classNode) {
        Set<String> annotations = new HashSet<>();
        
        // Class annotations
        if (classNode.visibleAnnotations != null) {
            for (org.objectweb.asm.tree.AnnotationNode annotation : classNode.visibleAnnotations) {
                annotations.add(Type.getType(annotation.desc).getClassName());
            }
        }
        
        // Method annotations
        if (classNode.methods != null) {
            for (MethodNode method : classNode.methods) {
                if (method.visibleAnnotations != null) {
                    for (org.objectweb.asm.tree.AnnotationNode annotation : method.visibleAnnotations) {
                        annotations.add(Type.getType(annotation.desc).getClassName());
                    }
                }
            }
        }
        
        // Field annotations
        if (classNode.fields != null) {
            for (FieldNode field : classNode.fields) {
                if (field.visibleAnnotations != null) {
                    for (org.objectweb.asm.tree.AnnotationNode annotation : field.visibleAnnotations) {
                        annotations.add(Type.getType(annotation.desc).getClassName());
                    }
                }
            }
        }
        
        return annotations;
    }
    
    /**
     * ASM visitor to extract dependencies from method calls and field references
     */
    private static class DependencyExtractor extends ClassVisitor {
        private final Set<String> dependencies;
        
        public DependencyExtractor(Set<String> dependencies) {
            super(Opcodes.ASM9);
            this.dependencies = dependencies;
        }
        
        @Override
        public MethodVisitor visitMethod(int access, String name, String descriptor, 
                                       String signature, String[] exceptions) {
            // Extract from method descriptor
            Type methodType = Type.getMethodType(descriptor);
            addType(methodType.getReturnType());
            for (Type argType : methodType.getArgumentTypes()) {
                addType(argType);
            }
            
            // Extract from exceptions
            if (exceptions != null) {
                for (String exception : exceptions) {
                    dependencies.add(exception.replace('/', '.'));
                }
            }
            
            return new MethodDependencyExtractor();
        }
        
        @Override
        public FieldVisitor visitField(int access, String name, String descriptor, 
                                     String signature, Object value) {
            // Extract from field descriptor
            Type fieldType = Type.getType(descriptor);
            addType(fieldType);
            
            return null;
        }
        
        private void addType(Type type) {
            if (type.getSort() == Type.OBJECT) {
                dependencies.add(type.getClassName());
            } else if (type.getSort() == Type.ARRAY) {
                addType(type.getElementType());
            }
        }
        
        private class MethodDependencyExtractor extends MethodVisitor {
            public MethodDependencyExtractor() {
                super(Opcodes.ASM9);
            }
            
            @Override
            public void visitTypeInsn(int opcode, String type) {
                dependencies.add(type.replace('/', '.'));
            }
            
            @Override
            public void visitFieldInsn(int opcode, String owner, String name, String descriptor) {
                dependencies.add(owner.replace('/', '.'));
                Type fieldType = Type.getType(descriptor);
                addType(fieldType);
            }
            
            @Override
            public void visitMethodInsn(int opcode, String owner, String name, 
                                      String descriptor, boolean isInterface) {
                dependencies.add(owner.replace('/', '.'));
                Type methodType = Type.getMethodType(descriptor);
                addType(methodType.getReturnType());
                for (Type argType : methodType.getArgumentTypes()) {
                    addType(argType);
                }
            }
        }
    }
    
    /**
     * Bytecode metadata container
     */
    public static class BytecodeMetadata {
        private String className;
        private int access;
        private int version;
        private String superClass;
        private Set<String> interfaces = new HashSet<>();
        private Set<String> methods = new HashSet<>();
        private Set<String> fields = new HashSet<>();
        
        // Getters and setters
        public String getClassName() { return className; }
        public void setClassName(String className) { this.className = className; }
        
        public int getAccess() { return access; }
        public void setAccess(int access) { this.access = access; }
        
        public int getVersion() { return version; }
        public void setVersion(int version) { this.version = version; }
        
        public String getSuperClass() { return superClass; }
        public void setSuperClass(String superClass) { this.superClass = superClass; }
        
        public Set<String> getInterfaces() { return interfaces; }
        public void addInterface(String interfaceName) { this.interfaces.add(interfaceName); }
        
        public Set<String> getMethods() { return methods; }
        public void addMethod(String methodName) { this.methods.add(methodName); }
        
        public Set<String> getFields() { return fields; }
        public void addField(String fieldName) { this.fields.add(fieldName); }
        
        public Map<String, Object> toMap() {
            Map<String, Object> map = new HashMap<>();
            if (className != null) map.put("className", className);
            map.put("access", access);
            map.put("version", version);
            if (superClass != null) map.put("superClass", superClass);
            map.put("interfaces", new ArrayList<>(interfaces));
            map.put("methods", new ArrayList<>(methods));
            map.put("fields", new ArrayList<>(fields));
            return map;
        }
    }

    /**
     * Extract servlet-specific information for mapping purposes
     */
    public Map<String, Object> extractServletMappingInfo(ClassNode classNode) {
        Map<String, Object> servletInfo = new HashMap<>();

        // Check if this is likely a servlet class
        boolean isServlet = isServletClass(classNode);
        servletInfo.put("isServlet", isServlet);

        if (isServlet) {
            // Extract servlet-specific methods
            Set<String> servletMethods = new HashSet<>();
            if (classNode.methods != null) {
                for (MethodNode method : classNode.methods) {
                    if (isServletMethod(method.name)) {
                        servletMethods.add(method.name + method.desc);
                    }
                }
            }
            servletInfo.put("servletMethods", servletMethods);

            // Extract JSP-related patterns in class name
            String className = classNode.name.replace('/', '.');
            String jspName = extractJspNameFromServletClass(className);
            if (jspName != null) {
                servletInfo.put("inferredJspName", jspName);
            }

            // Extract initialization parameters from annotations
            Map<String, String> initParams = extractInitParameters(classNode);
            servletInfo.put("initParameters", initParams);
        }

        return servletInfo;
    }

    /**
     * Check if a class is a servlet class
     */
    private boolean isServletClass(ClassNode classNode) {
        // Check superclass
        if (classNode.superName != null) {
            String superName = classNode.superName.replace('/', '.');
            if (superName.contains("HttpServlet") || superName.contains("GenericServlet")) {
                return true;
            }
        }

        // Check interfaces
        if (classNode.interfaces != null) {
            for (String interfaceName : classNode.interfaces) {
                String intfName = interfaceName.replace('/', '.');
                if (intfName.contains("Servlet")) {
                    return true;
                }
            }
        }

        // Check class name patterns
        String className = classNode.name.toLowerCase();
        return className.contains("servlet") || className.contains("_jsp") || className.endsWith("jsp");
    }

    /**
     * Check if a method is a servlet method
     */
    private boolean isServletMethod(String methodName) {
        return methodName.equals("doGet") || methodName.equals("doPost") ||
               methodName.equals("doPut") || methodName.equals("doDelete") ||
               methodName.equals("service") || methodName.contains("_jspService");
    }

    /**
     * Extract JSP name from servlet class name
     */
    private String extractJspNameFromServletClass(String className) {
        String simpleName = className.substring(className.lastIndexOf('.') + 1);

        // Try different patterns
        if (simpleName.endsWith("_jsp")) {
            return simpleName.substring(0, simpleName.length() - 4);
        }
        if (simpleName.endsWith("JSP")) {
            return simpleName.substring(0, simpleName.length() - 3);
        }
        if (simpleName.endsWith("$jsp")) {
            return simpleName.substring(0, simpleName.length() - 4);
        }

        return null;
    }

    /**
     * Extract initialization parameters from annotations
     */
    private Map<String, String> extractInitParameters(ClassNode classNode) {
        Map<String, String> initParams = new HashMap<>();

        // This would need to be implemented based on specific annotation patterns
        // For now, return empty map

        return initParams;
    }

    /**
     * Enhanced bytecode analysis with servlet mapping information
     */
    public ComponentModel analyzeBytecodeWithServletInfo(InputStream is, String path) throws IOException {
        ComponentModel component = analyzeClassInputStream(is, path);

        if (component != null) {
            ClassReader classReader = new ClassReader(is);
            ClassNode classNode = new ClassNode();
            classReader.accept(classNode, ClassReader.SKIP_DEBUG);

            Map<String, Object> servletInfo = extractServletMappingInfo(classNode);
            component.getProperties().put("servletMappingInfo", servletInfo);
        }

        return component;
    }
}
