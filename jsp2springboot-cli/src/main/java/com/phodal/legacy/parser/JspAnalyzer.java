package com.phodal.legacy.parser;

import com.phodal.legacy.model.ComponentModel;
import com.phodal.legacy.model.JspBytecodeMapping;
import com.phodal.legacy.model.MappingRegistry;
import com.phodal.legacy.utils.FileUtils;
import com.phodal.legacy.utils.LoggingUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Stream;

/**
 * Analyzer for JSP files to extract components, dependencies, and structure information.
 * Parses JSP directives, scriptlets, expressions, and identifies Spring Boot migration requirements.
 */
public class JspAnalyzer {
    
    private static final Logger logger = LoggerFactory.getLogger(JspAnalyzer.class);
    
    // JSP directive patterns
    private static final Pattern PAGE_DIRECTIVE_PATTERN = Pattern.compile(
        "<%@\\s*page\\s+([^%>]+)%>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
    private static final Pattern INCLUDE_DIRECTIVE_PATTERN = Pattern.compile(
        "<%@\\s*include\\s+file\\s*=\\s*[\"']([^\"']+)[\"']\\s*%>", Pattern.CASE_INSENSITIVE);
    private static final Pattern TAGLIB_DIRECTIVE_PATTERN = Pattern.compile(
        "<%@\\s*taglib\\s+([^%>]+)%>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
    
    // JSP scriptlet and expression patterns
    private static final Pattern SCRIPTLET_PATTERN = Pattern.compile(
        "<%([^@%][^%]*)%>", Pattern.DOTALL);
    private static final Pattern EXPRESSION_PATTERN = Pattern.compile(
        "<%=([^%]+)%>", Pattern.DOTALL);
    private static final Pattern DECLARATION_PATTERN = Pattern.compile(
        "<%!([^%]+)%>", Pattern.DOTALL);
    
    // JSP tag patterns
    private static final Pattern JSP_TAG_PATTERN = Pattern.compile(
        "<jsp:([^\\s>]+)([^>]*)>", Pattern.CASE_INSENSITIVE);
    private static final Pattern CUSTOM_TAG_PATTERN = Pattern.compile(
        "<([a-zA-Z][a-zA-Z0-9]*):([^\\s>]+)([^>]*)>", Pattern.CASE_INSENSITIVE);
    
    // Import and class usage patterns
    private static final Pattern IMPORT_PATTERN = Pattern.compile(
        "import\\s+([a-zA-Z_][a-zA-Z0-9_.]*);", Pattern.CASE_INSENSITIVE);
    private static final Pattern CLASS_USAGE_PATTERN = Pattern.compile(
        "\\b([A-Z][a-zA-Z0-9_]*(?:\\.[A-Z][a-zA-Z0-9_]*)*)\\b");
    
    /**
     * Analyze all JSP files in the given directory
     */
    public List<ComponentModel> analyzeJspFiles(Path projectRoot) throws IOException {
        LoggingUtils.logOperationStart("analyzeJspFiles", "JspAnalyzer");
        
        List<ComponentModel> jspComponents = new ArrayList<>();
        
        try (Stream<Path> paths = Files.walk(projectRoot)) {
            List<Path> jspFiles = paths
                .filter(Files::isRegularFile)
                .filter(path -> isJspFile(path))
                .toList();
            
            logger.info("Found {} JSP files to analyze", jspFiles.size());
            
            for (int i = 0; i < jspFiles.size(); i++) {
                Path jspFile = jspFiles.get(i);
                LoggingUtils.logProgress(i + 1, jspFiles.size(), "Analyzing JSP: " + jspFile.getFileName());
                
                try {
                    ComponentModel component = analyzeJspFile(jspFile, projectRoot);
                    if (component != null) {
                        jspComponents.add(component);
                    }
                } catch (Exception e) {
                    logger.warn("Failed to analyze JSP file: {} - {}", jspFile, e.getMessage());
                }
            }
            
            LoggingUtils.logOperationComplete("analyzeJspFiles", "JspAnalyzer");
            return jspComponents;
            
        } catch (IOException e) {
            LoggingUtils.logOperationError("analyzeJspFiles", "JspAnalyzer", e);
            throw e;
        }
    }
    
    /**
     * Analyze a single JSP file
     */
    public ComponentModel analyzeJspFile(Path jspFile, Path projectRoot) throws IOException {
        LoggingUtils.logFileProcessing(jspFile.toString(), "Analyzing JSP");
        
        String content = FileUtils.readFileContent(jspFile);
        String relativePath = FileUtils.getRelativePath(projectRoot, jspFile);
        
        ComponentModel component = new ComponentModel(
            relativePath,
            jspFile.getFileName().toString(),
            ComponentModel.ComponentType.JSP_PAGE
        );
        component.setSourcePath(jspFile.toString());
        
        // Extract JSP metadata
        JspMetadata metadata = extractJspMetadata(content);
        component.getProperties().putAll(metadata.toMap());

        // Extract dependencies
        Set<String> dependencies = extractDependencies(content);
        component.getDependencies().addAll(dependencies);

        // Add imports from metadata to dependencies
        component.getDependencies().addAll(metadata.getImports());

        // Extract includes
        Set<String> includes = extractIncludes(content);
        component.getProperties().put("includes", new ArrayList<>(includes));

        // Extract tag libraries
        Set<String> tagLibs = extractTagLibraries(content);
        component.getProperties().put("tagLibraries", new ArrayList<>(tagLibs));

        // Analyze complexity
        int complexity = calculateComplexity(content);
        component.getProperties().put("complexity", complexity);
        
        LoggingUtils.logFileProcessed(jspFile.toString(), "Analyzed JSP");
        return component;
    }
    
    /**
     * Check if a file is a JSP file
     */
    private boolean isJspFile(Path path) {
        String fileName = path.getFileName().toString().toLowerCase();
        return fileName.endsWith(".jsp") || fileName.endsWith(".jspx") || fileName.endsWith(".jspf");
    }
    
    /**
     * Extract JSP metadata from content
     */
    private JspMetadata extractJspMetadata(String content) {
        JspMetadata metadata = new JspMetadata();
        
        // Extract page directive attributes
        Matcher pageDirectiveMatcher = PAGE_DIRECTIVE_PATTERN.matcher(content);
        while (pageDirectiveMatcher.find()) {
            String directiveContent = pageDirectiveMatcher.group(1);
            parsePageDirective(directiveContent, metadata);
        }
        
        return metadata;
    }
    
    /**
     * Parse page directive attributes
     */
    private void parsePageDirective(String directiveContent, JspMetadata metadata) {
        // Parse common page directive attributes
        Map<String, String> attributes = parseAttributes(directiveContent);
        
        if (attributes.containsKey("language")) {
            metadata.setLanguage(attributes.get("language"));
        }
        if (attributes.containsKey("contentType")) {
            metadata.setContentType(attributes.get("contentType"));
        }
        if (attributes.containsKey("pageEncoding")) {
            metadata.setPageEncoding(attributes.get("pageEncoding"));
        }
        if (attributes.containsKey("session")) {
            metadata.setSessionEnabled("true".equalsIgnoreCase(attributes.get("session")));
        }
        if (attributes.containsKey("buffer")) {
            metadata.setBuffer(attributes.get("buffer"));
        }
        if (attributes.containsKey("autoFlush")) {
            metadata.setAutoFlush("true".equalsIgnoreCase(attributes.get("autoFlush")));
        }
        if (attributes.containsKey("errorPage")) {
            metadata.setErrorPage(attributes.get("errorPage"));
        }
        if (attributes.containsKey("isErrorPage")) {
            metadata.setErrorPage("true".equalsIgnoreCase(attributes.get("isErrorPage")) ? "true" : null);
        }
        if (attributes.containsKey("import")) {
            String imports = attributes.get("import");
            for (String imp : imports.split(",")) {
                metadata.addImport(imp.trim());
            }
        }
    }
    
    /**
     * Parse attribute string into key-value pairs
     */
    private Map<String, String> parseAttributes(String attributeString) {
        Map<String, String> attributes = new HashMap<>();
        
        // Simple attribute parsing - handles quoted values
        Pattern attrPattern = Pattern.compile("(\\w+)\\s*=\\s*[\"']([^\"']*)[\"']");
        Matcher matcher = attrPattern.matcher(attributeString);
        
        while (matcher.find()) {
            attributes.put(matcher.group(1), matcher.group(2));
        }
        
        return attributes;
    }
    
    /**
     * Extract dependencies from JSP content
     */
    private Set<String> extractDependencies(String content) {
        Set<String> dependencies = new HashSet<>();
        
        // Extract from imports in page directives
        Matcher importMatcher = IMPORT_PATTERN.matcher(content);
        while (importMatcher.find()) {
            dependencies.add(importMatcher.group(1));
        }
        
        // Extract from class usage patterns
        Matcher classMatcher = CLASS_USAGE_PATTERN.matcher(content);
        while (classMatcher.find()) {
            String className = classMatcher.group(1);
            if (isLikelyJavaClass(className)) {
                dependencies.add(className);
            }
        }
        
        return dependencies;
    }
    
    /**
     * Extract include files from JSP content
     */
    private Set<String> extractIncludes(String content) {
        Set<String> includes = new HashSet<>();
        
        Matcher includeMatcher = INCLUDE_DIRECTIVE_PATTERN.matcher(content);
        while (includeMatcher.find()) {
            includes.add(includeMatcher.group(1));
        }
        
        return includes;
    }
    
    /**
     * Extract tag libraries from JSP content
     */
    private Set<String> extractTagLibraries(String content) {
        Set<String> tagLibs = new HashSet<>();
        
        Matcher taglibMatcher = TAGLIB_DIRECTIVE_PATTERN.matcher(content);
        while (taglibMatcher.find()) {
            String directiveContent = taglibMatcher.group(1);
            Map<String, String> attributes = parseAttributes(directiveContent);
            
            if (attributes.containsKey("uri")) {
                tagLibs.add(attributes.get("uri"));
            }
        }
        
        return tagLibs;
    }
    
    /**
     * Calculate complexity score for JSP file
     */
    private int calculateComplexity(String content) {
        int complexity = 0;
        
        // Count scriptlets
        Matcher scriptletMatcher = SCRIPTLET_PATTERN.matcher(content);
        while (scriptletMatcher.find()) {
            complexity += 2; // Scriptlets add complexity
        }
        
        // Count expressions
        Matcher expressionMatcher = EXPRESSION_PATTERN.matcher(content);
        while (expressionMatcher.find()) {
            complexity += 1; // Expressions add some complexity
        }
        
        // Count declarations
        Matcher declarationMatcher = DECLARATION_PATTERN.matcher(content);
        while (declarationMatcher.find()) {
            complexity += 3; // Declarations add more complexity
        }
        
        // Count custom tags
        Matcher customTagMatcher = CUSTOM_TAG_PATTERN.matcher(content);
        while (customTagMatcher.find()) {
            complexity += 1; // Custom tags add complexity
        }
        
        return complexity;
    }
    
    /**
     * Check if a string is likely a Java class name
     */
    private boolean isLikelyJavaClass(String className) {
        return className.matches("^[A-Z][a-zA-Z0-9_]*(?:\\.[A-Z][a-zA-Z0-9_]*)*$") &&
               !className.equals("String") && !className.equals("Object") &&
               !className.equals("Integer") && !className.equals("Boolean");
    }

    /**
     * Extract additional mapping information from JSP content for better mapping accuracy
     */
    public Map<String, Object> extractMappingHints(String content) {
        Map<String, Object> hints = new HashMap<>();

        // Extract servlet class hints from page directives
        Matcher pageDirectiveMatcher = PAGE_DIRECTIVE_PATTERN.matcher(content);
        while (pageDirectiveMatcher.find()) {
            String directiveContent = pageDirectiveMatcher.group(1);
            Map<String, String> attributes = parseAttributes(directiveContent);

            if (attributes.containsKey("extends")) {
                hints.put("extendsClass", attributes.get("extends"));
            }
            if (attributes.containsKey("implements")) {
                hints.put("implementsInterface", attributes.get("implements"));
            }
        }

        // Extract method signatures from scriptlets and declarations
        Set<String> methodSignatures = new HashSet<>();

        // From declarations
        Matcher declarationMatcher = DECLARATION_PATTERN.matcher(content);
        while (declarationMatcher.find()) {
            String declaration = declarationMatcher.group(1);
            extractMethodSignatures(declaration, methodSignatures);
        }

        hints.put("methodSignatures", methodSignatures);

        // Extract variable declarations
        Set<String> variables = new HashSet<>();
        Matcher scriptletMatcher = SCRIPTLET_PATTERN.matcher(content);
        while (scriptletMatcher.find()) {
            String scriptlet = scriptletMatcher.group(1);
            extractVariableDeclarations(scriptlet, variables);
        }

        hints.put("variables", variables);

        return hints;
    }

    /**
     * Extract method signatures from Java code
     */
    private void extractMethodSignatures(String javaCode, Set<String> methodSignatures) {
        // Simple pattern to match method declarations
        Pattern methodPattern = Pattern.compile(
            "(public|private|protected)?\\s*(static)?\\s*([\\w<>\\[\\]]+)\\s+(\\w+)\\s*\\([^)]*\\)",
            Pattern.MULTILINE
        );

        Matcher matcher = methodPattern.matcher(javaCode);
        while (matcher.find()) {
            String methodSignature = matcher.group().trim();
            methodSignatures.add(methodSignature);
        }
    }

    /**
     * Extract variable declarations from Java code
     */
    private void extractVariableDeclarations(String javaCode, Set<String> variables) {
        // Simple pattern to match variable declarations
        Pattern varPattern = Pattern.compile(
            "\\b([A-Z][\\w<>\\[\\]]*|int|long|double|float|boolean|char|byte|short)\\s+(\\w+)\\s*[=;]",
            Pattern.MULTILINE
        );

        Matcher matcher = varPattern.matcher(javaCode);
        while (matcher.find()) {
            String varType = matcher.group(1);
            String varName = matcher.group(2);
            variables.add(varType + " " + varName);
        }
    }

    /**
     * Enhanced JSP analysis with mapping hints
     */
    public ComponentModel analyzeJspFileWithMappingHints(Path jspFile, Path projectRoot) throws IOException {
        ComponentModel component = analyzeJspFile(jspFile, projectRoot);

        if (component != null) {
            String content = FileUtils.readFileContent(jspFile);
            Map<String, Object> mappingHints = extractMappingHints(content);

            // Add mapping hints to component properties
            component.getProperties().put("mappingHints", mappingHints);
        }

        return component;
    }

    /**
     * JSP metadata container
     */
    public static class JspMetadata {
        private String language = "java";
        private String contentType = "text/html";
        private String pageEncoding = "UTF-8";
        private boolean sessionEnabled = true;
        private String buffer = "8kb";
        private boolean autoFlush = true;
        private String errorPage;
        private Set<String> imports = new HashSet<>();
        
        // Getters and setters
        public String getLanguage() { return language; }
        public void setLanguage(String language) { this.language = language; }
        
        public String getContentType() { return contentType; }
        public void setContentType(String contentType) { this.contentType = contentType; }
        
        public String getPageEncoding() { return pageEncoding; }
        public void setPageEncoding(String pageEncoding) { this.pageEncoding = pageEncoding; }
        
        public boolean isSessionEnabled() { return sessionEnabled; }
        public void setSessionEnabled(boolean sessionEnabled) { this.sessionEnabled = sessionEnabled; }
        
        public String getBuffer() { return buffer; }
        public void setBuffer(String buffer) { this.buffer = buffer; }
        
        public boolean isAutoFlush() { return autoFlush; }
        public void setAutoFlush(boolean autoFlush) { this.autoFlush = autoFlush; }
        
        public String getErrorPage() { return errorPage; }
        public void setErrorPage(String errorPage) { this.errorPage = errorPage; }
        
        public Set<String> getImports() { return imports; }
        public void addImport(String importStr) { this.imports.add(importStr); }
        
        public Map<String, Object> toMap() {
            Map<String, Object> map = new HashMap<>();
            map.put("language", language);
            map.put("contentType", contentType);
            map.put("pageEncoding", pageEncoding);
            map.put("sessionEnabled", sessionEnabled);
            map.put("buffer", buffer);
            map.put("autoFlush", autoFlush);
            if (errorPage != null) map.put("errorPage", errorPage);
            map.put("imports", new ArrayList<>(imports));
            return map;
        }
    }
}
