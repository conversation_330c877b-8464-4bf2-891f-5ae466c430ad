package com.phodal.legacy.examples;

import com.phodal.legacy.mapper.JspBytecodeMapper;
import com.phodal.legacy.model.ComponentModel;
import com.phodal.legacy.model.JspBytecodeMapping;
import com.phodal.legacy.model.MappingRegistry;
import com.phodal.legacy.services.AnalysisService;

import java.util.Arrays;
import java.util.List;

/**
 * Example demonstrating JSP to bytecode mapping functionality.
 * This class shows how to use the mapping system to establish relationships
 * between JSP files and their compiled servlet counterparts.
 */
public class MappingExample {
    
    public static void main(String[] args) {
        System.out.println("=== JSP to Bytecode Mapping Example ===\n");
        
        // Create example JSP components
        List<ComponentModel> jspComponents = createExampleJspComponents();
        
        // Create example bytecode components
        List<ComponentModel> bytecodeComponents = createExampleBytecodeComponents();
        
        // Create mapper and establish mappings
        JspBytecodeMapper mapper = new JspBytecodeMapper();
        MappingRegistry registry = mapper.createMappings(jspComponents, bytecodeComponents);
        
        // Display mapping results
        displayMappingResults(registry);
        
        // Demonstrate mapping queries
        demonstrateMappingQueries(registry);
    }
    
    private static List<ComponentModel> createExampleJspComponents() {
        System.out.println("Creating example JSP components...");
        
        // Create index.jsp component
        ComponentModel indexJsp = new ComponentModel("jsp1", "index.jsp", ComponentModel.ComponentType.JSP_PAGE);
        indexJsp.setSourcePath("/webapp/index.jsp");
        indexJsp.addDependency("java.util.List");
        indexJsp.addDependency("com.example.UserService");
        indexJsp.addProperty("complexity", 5);
        indexJsp.getProperties().put("tagLibraries", Arrays.asList(
            "http://java.sun.com/jsp/jstl/core",
            "http://java.sun.com/jsp/jstl/fmt"
        ));
        
        // Create login.jsp component
        ComponentModel loginJsp = new ComponentModel("jsp2", "login.jsp", ComponentModel.ComponentType.JSP_PAGE);
        loginJsp.setSourcePath("/webapp/login.jsp");
        loginJsp.addDependency("java.util.Map");
        loginJsp.addDependency("com.example.AuthService");
        loginJsp.addProperty("complexity", 3);
        loginJsp.getProperties().put("tagLibraries", Arrays.asList(
            "http://java.sun.com/jsp/jstl/core"
        ));
        
        // Create admin/dashboard.jsp component
        ComponentModel dashboardJsp = new ComponentModel("jsp3", "dashboard.jsp", ComponentModel.ComponentType.JSP_PAGE);
        dashboardJsp.setSourcePath("/webapp/admin/dashboard.jsp");
        dashboardJsp.addDependency("java.util.List");
        dashboardJsp.addDependency("java.util.Map");
        dashboardJsp.addDependency("com.example.AdminService");
        dashboardJsp.addProperty("complexity", 8);
        
        System.out.println("✓ Created 3 JSP components\n");
        return Arrays.asList(indexJsp, loginJsp, dashboardJsp);
    }
    
    private static List<ComponentModel> createExampleBytecodeComponents() {
        System.out.println("Creating example bytecode components...");
        
        // Create index_jsp servlet
        ComponentModel indexServlet = new ComponentModel("servlet1", "index_jsp", ComponentModel.ComponentType.SERVLET);
        indexServlet.addProperty("className", "org.apache.jsp.index_jsp");
        indexServlet.addProperty("methods", Arrays.asList("_jspService", "doGet", "doPost"));
        indexServlet.addDependency("javax.servlet.http.HttpServlet");
        indexServlet.addDependency("java.util.List");
        indexServlet.addDependency("com.example.UserService");
        
        // Create login_jsp servlet
        ComponentModel loginServlet = new ComponentModel("servlet2", "login_jsp", ComponentModel.ComponentType.SERVLET);
        loginServlet.addProperty("className", "org.apache.jsp.login_jsp");
        loginServlet.addProperty("methods", Arrays.asList("_jspService", "doGet", "doPost"));
        loginServlet.addDependency("javax.servlet.http.HttpServlet");
        loginServlet.addDependency("java.util.Map");
        loginServlet.addDependency("com.example.AuthService");
        
        // Create dashboard_jsp servlet
        ComponentModel dashboardServlet = new ComponentModel("servlet3", "admin.dashboard_jsp", ComponentModel.ComponentType.SERVLET);
        dashboardServlet.addProperty("className", "org.apache.jsp.admin.dashboard_jsp");
        dashboardServlet.addProperty("methods", Arrays.asList("_jspService", "doGet", "doPost"));
        dashboardServlet.addDependency("javax.servlet.http.HttpServlet");
        dashboardServlet.addDependency("java.util.List");
        dashboardServlet.addDependency("java.util.Map");
        dashboardServlet.addDependency("com.example.AdminService");
        
        // Create a non-servlet component (should not be mapped)
        ComponentModel userService = new ComponentModel("java1", "UserService", ComponentModel.ComponentType.JAVA_CLASS);
        userService.addProperty("className", "com.example.UserService");
        userService.addProperty("methods", Arrays.asList("getUser", "saveUser", "deleteUser"));
        
        System.out.println("✓ Created 4 bytecode components (3 servlets, 1 regular class)\n");
        return Arrays.asList(indexServlet, loginServlet, dashboardServlet, userService);
    }
    
    private static void displayMappingResults(MappingRegistry registry) {
        System.out.println("=== Mapping Results ===");
        
        MappingRegistry.MappingStatistics stats = registry.getStatistics();
        System.out.println("Total mappings created: " + stats.totalMappings);
        System.out.println("Total bytecode classes mapped: " + stats.totalBytecodeClasses);
        System.out.println("Total dependencies mapped: " + stats.totalDependencies);
        System.out.println("Total class mappings: " + stats.totalClassMappings);
        System.out.println("Total method mappings: " + stats.totalMethodMappings);
        System.out.println("Total dependency mappings: " + stats.totalDependencyMappings);
        System.out.println("Total tag library mappings: " + stats.totalTagLibraryMappings);
        System.out.println();
        
        // Display individual mappings
        System.out.println("Individual mappings:");
        for (JspBytecodeMapping mapping : registry.getAllMappings()) {
            System.out.println("  " + mapping.getJspPath() + " -> " + mapping.getCompiledServletClass());
            
            // Show confidence score
            Object confidence = mapping.getMetadata().get("mappingConfidence");
            if (confidence instanceof Double) {
                System.out.printf("    Confidence: %.2f%%\n", ((Double) confidence) * 100);
            }
            
            // Show dependency mappings
            if (!mapping.getDependencyMappings().isEmpty()) {
                System.out.println("    Dependencies: " + mapping.getDependencyMappings().size());
            }
            
            // Show tag libraries
            if (!mapping.getTagLibraryMappings().isEmpty()) {
                System.out.println("    Tag Libraries: " + mapping.getTagLibraryMappings());
            }
            
            System.out.println();
        }
    }
    
    private static void demonstrateMappingQueries(MappingRegistry registry) {
        System.out.println("=== Mapping Queries ===");
        
        // Query by JSP path
        JspBytecodeMapping indexMapping = registry.getMappingByPath("/webapp/index.jsp");
        if (indexMapping != null) {
            System.out.println("Found mapping for /webapp/index.jsp:");
            System.out.println("  Servlet class: " + indexMapping.getCompiledServletClass());
            System.out.println("  Dependencies: " + indexMapping.getDependencyMappings().size());
        }
        
        // Query by bytecode class
        var mappingsForIndexServlet = registry.getMappingsByBytecodeClass("org.apache.jsp.index_jsp");
        System.out.println("\nMappings using org.apache.jsp.index_jsp: " + mappingsForIndexServlet.size());
        
        // Query by dependency
        var mappingsUsingList = registry.getMappingsByDependency("java.util.List");
        System.out.println("Mappings using java.util.List dependency: " + mappingsUsingList.size());
        
        // Show all mapped bytecode classes
        System.out.println("\nAll mapped bytecode classes:");
        for (String className : registry.getMappedBytecodeClasses()) {
            System.out.println("  " + className);
        }
        
        // Show all mapped dependencies
        System.out.println("\nAll mapped dependencies:");
        for (String dependency : registry.getMappedDependencies()) {
            System.out.println("  " + dependency);
        }
        
        System.out.println("\n=== Example Complete ===");
    }
}
