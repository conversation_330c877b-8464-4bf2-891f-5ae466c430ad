package com.phodal.legacy.mapper;

import com.phodal.legacy.model.ComponentModel;
import com.phodal.legacy.model.JspBytecodeMapping;
import com.phodal.legacy.model.MappingRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for JspBytecodeMapper to verify mapping functionality.
 */
class JspBytecodeMapperTest {
    
    private JspBytecodeMapper mapper;
    
    @BeforeEach
    void setUp() {
        mapper = new JspBytecodeMapper();
    }
    
    @Test
    void testCreateMappingsWithEmptyLists() {
        List<ComponentModel> jspComponents = new ArrayList<>();
        List<ComponentModel> bytecodeComponents = new ArrayList<>();
        
        MappingRegistry registry = mapper.createMappings(jspComponents, bytecodeComponents);
        
        assertNotNull(registry);
        assertTrue(registry.getAllMappings().isEmpty());
    }
    
    @Test
    void testCreateMappingsWithMatchingComponents() {
        // Create JSP component
        ComponentModel jspComponent = new ComponentModel("jsp1", "index.jsp", ComponentModel.ComponentType.JSP_PAGE);
        jspComponent.setSourcePath("/webapp/index.jsp");
        jspComponent.addDependency("java.util.List");
        jspComponent.addDependency("com.example.UserService");
        
        // Create corresponding servlet component
        ComponentModel servletComponent = new ComponentModel("servlet1", "index_jsp", ComponentModel.ComponentType.SERVLET);
        servletComponent.addProperty("className", "org.apache.jsp.index_jsp");
        servletComponent.addProperty("methods", Arrays.asList("_jspService", "doGet", "doPost"));
        servletComponent.addDependency("javax.servlet.http.HttpServlet");
        servletComponent.addDependency("java.util.List");
        servletComponent.addDependency("com.example.UserService");
        
        List<ComponentModel> jspComponents = Arrays.asList(jspComponent);
        List<ComponentModel> bytecodeComponents = Arrays.asList(servletComponent);
        
        MappingRegistry registry = mapper.createMappings(jspComponents, bytecodeComponents);
        
        assertNotNull(registry);
        assertEquals(1, registry.getAllMappings().size());
        
        JspBytecodeMapping mapping = registry.getMapping("jsp1");
        assertNotNull(mapping);
        assertEquals("/webapp/index.jsp", mapping.getJspPath());
        assertEquals("org.apache.jsp.index_jsp", mapping.getCompiledServletClass());
        
        // Verify class mappings
        assertTrue(mapping.hasClassMapping("index.jsp"));
        assertEquals("org.apache.jsp.index_jsp", mapping.getBytecodeClass("index.jsp"));
        
        // Verify dependency mappings
        assertTrue(mapping.getDependencyMappings().containsKey("java.util.List"));
        assertTrue(mapping.getDependencyMappings().containsKey("com.example.UserService"));
    }
    
    @Test
    void testCreateMappingsWithMultipleJspFiles() {
        // Create multiple JSP components
        ComponentModel jsp1 = new ComponentModel("jsp1", "index.jsp", ComponentModel.ComponentType.JSP_PAGE);
        jsp1.setSourcePath("/webapp/index.jsp");
        
        ComponentModel jsp2 = new ComponentModel("jsp2", "login.jsp", ComponentModel.ComponentType.JSP_PAGE);
        jsp2.setSourcePath("/webapp/login.jsp");
        
        ComponentModel jsp3 = new ComponentModel("jsp3", "admin.jsp", ComponentModel.ComponentType.JSP_PAGE);
        jsp3.setSourcePath("/webapp/admin/admin.jsp");
        
        // Create corresponding servlet components
        ComponentModel servlet1 = new ComponentModel("servlet1", "index_jsp", ComponentModel.ComponentType.SERVLET);
        servlet1.addProperty("className", "org.apache.jsp.index_jsp");
        
        ComponentModel servlet2 = new ComponentModel("servlet2", "login_jsp", ComponentModel.ComponentType.SERVLET);
        servlet2.addProperty("className", "org.apache.jsp.login_jsp");
        
        ComponentModel servlet3 = new ComponentModel("servlet3", "admin_jsp", ComponentModel.ComponentType.SERVLET);
        servlet3.addProperty("className", "org.apache.jsp.admin.admin_jsp");
        
        List<ComponentModel> jspComponents = Arrays.asList(jsp1, jsp2, jsp3);
        List<ComponentModel> bytecodeComponents = Arrays.asList(servlet1, servlet2, servlet3);
        
        MappingRegistry registry = mapper.createMappings(jspComponents, bytecodeComponents);
        
        assertNotNull(registry);
        assertEquals(3, registry.getAllMappings().size());
        
        // Verify all mappings exist
        assertNotNull(registry.getMapping("jsp1"));
        assertNotNull(registry.getMapping("jsp2"));
        assertNotNull(registry.getMapping("jsp3"));
    }
    
    @Test
    void testCreateMappingsWithNoMatchingServlets() {
        // Create JSP component
        ComponentModel jspComponent = new ComponentModel("jsp1", "index.jsp", ComponentModel.ComponentType.JSP_PAGE);
        jspComponent.setSourcePath("/webapp/index.jsp");
        
        // Create non-matching bytecode component
        ComponentModel javaComponent = new ComponentModel("java1", "UserService", ComponentModel.ComponentType.JAVA_CLASS);
        javaComponent.addProperty("className", "com.example.UserService");
        
        List<ComponentModel> jspComponents = Arrays.asList(jspComponent);
        List<ComponentModel> bytecodeComponents = Arrays.asList(javaComponent);
        
        MappingRegistry registry = mapper.createMappings(jspComponents, bytecodeComponents);
        
        assertNotNull(registry);
        assertTrue(registry.getAllMappings().isEmpty());
    }
    
    @Test
    void testCreateMappingsWithDifferentNamingConventions() {
        // Test different servlet naming patterns
        ComponentModel jsp = new ComponentModel("jsp1", "test.jsp", ComponentModel.ComponentType.JSP_PAGE);
        jsp.setSourcePath("/webapp/test.jsp");
        
        // Test Tomcat style naming
        ComponentModel servlet1 = new ComponentModel("servlet1", "test_jsp", ComponentModel.ComponentType.SERVLET);
        servlet1.addProperty("className", "org.apache.jsp.test_jsp");
        
        List<ComponentModel> jspComponents = Arrays.asList(jsp);
        List<ComponentModel> bytecodeComponents = Arrays.asList(servlet1);
        
        MappingRegistry registry = mapper.createMappings(jspComponents, bytecodeComponents);
        
        assertEquals(1, registry.getAllMappings().size());
        
        JspBytecodeMapping mapping = registry.getMapping("jsp1");
        assertNotNull(mapping);
        assertEquals("org.apache.jsp.test_jsp", mapping.getCompiledServletClass());
    }
    
    @Test
    void testMappingConfidenceCalculation() {
        ComponentModel jsp = new ComponentModel("jsp1", "index.jsp", ComponentModel.ComponentType.JSP_PAGE);
        jsp.setSourcePath("/webapp/index.jsp");
        jsp.addDependency("java.util.List");
        jsp.addDependency("com.example.Service");
        
        ComponentModel servlet = new ComponentModel("servlet1", "index_jsp", ComponentModel.ComponentType.SERVLET);
        servlet.addProperty("className", "org.apache.jsp.index_jsp");
        servlet.addDependency("javax.servlet.http.HttpServlet");
        servlet.addDependency("java.util.List"); // Common dependency
        servlet.addDependency("com.example.Service"); // Common dependency
        
        List<ComponentModel> jspComponents = Arrays.asList(jsp);
        List<ComponentModel> bytecodeComponents = Arrays.asList(servlet);
        
        MappingRegistry registry = mapper.createMappings(jspComponents, bytecodeComponents);
        
        JspBytecodeMapping mapping = registry.getMapping("jsp1");
        assertNotNull(mapping);
        
        // Check that confidence is calculated and stored in metadata
        Object confidence = mapping.getMetadata().get("mappingConfidence");
        assertNotNull(confidence);
        assertTrue(confidence instanceof Double);
        assertTrue((Double) confidence > 0.5); // Should have high confidence due to name match and common dependencies
    }
    
    @Test
    void testMappingRegistryOperations() {
        ComponentModel jsp = new ComponentModel("jsp1", "test.jsp", ComponentModel.ComponentType.JSP_PAGE);
        jsp.setSourcePath("/webapp/test.jsp");
        
        ComponentModel servlet = new ComponentModel("servlet1", "test_jsp", ComponentModel.ComponentType.SERVLET);
        servlet.addProperty("className", "org.apache.jsp.test_jsp");
        
        List<ComponentModel> jspComponents = Arrays.asList(jsp);
        List<ComponentModel> bytecodeComponents = Arrays.asList(servlet);
        
        MappingRegistry registry = mapper.createMappings(jspComponents, bytecodeComponents);
        
        // Test registry operations
        assertTrue(registry.hasMapping("jsp1"));
        assertFalse(registry.hasMapping("nonexistent"));
        
        assertTrue(registry.isBytecodeClassMapped("org.apache.jsp.test_jsp"));
        assertFalse(registry.isBytecodeClassMapped("com.example.NonExistent"));
        
        assertEquals(1, registry.getMappedBytecodeClasses().size());
        assertTrue(registry.getMappedBytecodeClasses().contains("org.apache.jsp.test_jsp"));
        
        // Test statistics
        MappingRegistry.MappingStatistics stats = registry.getStatistics();
        assertEquals(1, stats.totalMappings);
        assertEquals(1, stats.totalBytecodeClasses);
    }
}
